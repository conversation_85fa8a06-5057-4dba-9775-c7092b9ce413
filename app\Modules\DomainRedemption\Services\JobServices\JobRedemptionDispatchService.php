<?php

namespace App\Modules\DomainRedemption\Services\JobServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\DomainRedemption\Jobs\ProcessDomainRedemptionJob;
use App\Modules\DomainRedemption\Jobs\SuccessDomainRedemptionJob;
use Illuminate\Support\Facades\DB;

class JobRedemptionDispatchService
{
    use UserLoggerTrait;

    private $isJob = true;

    public static function instance(): self
    {
        return new self;
    }

    public function redemptionEppDispatch(object $domain, int $userId, array $refundDetails, int $seconds = 0): void
    {
        ProcessDomainRedemptionJob::dispatch($domain, $userId, $refundDetails)->delay($seconds);
        app(AuthLogger::class)->info($this->fromWho("Redemption job dispatched: {$domain->domain_name}", $this->getUserEmail($userId)));
    }

    public function redemptionSuccessDispatch(object $domain, int $userId, array $refundDetails = [], int $seconds = 60): void
    {
        SuccessDomainRedemptionJob::dispatch($domain, $userId, $refundDetails)->delay($seconds);
        app(AuthLogger::class)->info($this->fromWho("Redemption success job dispatched: {$domain->domain_name}", $this->getUserEmail($userId)));
    }

    private function getUserEmail(int $userId): string
    
    {
        $user = DB::table('users')->where('id', $userId)->first();
        return $user->email;
    }
}
