<?php

namespace App\Modules\Payment\Services;

use App\Events\SystemLogEvent;
use App\Models\PaymentNode;
use App\Models\PaymentNodeInvoice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Modules\Payment\Constants\PaymentFees;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\Store\BulkActions;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class PaymentNodeService
{
    use UserLoggerTrait;

    private $icann_fee;

    public function __construct()
    {
        $this->icann_fee = floatval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_ICANN_FEE));
    }

    public static function instance(): self
    {
        $paymentNode = new self;

        return $paymentNode;
    }

    public function store(array $data): void
    {
        $type = $data['type'];
        $registeredDomains = $data['registered_domains'];
        $invoice = $data['invoice'];
        $otherFees = $data['other_fees'];
        $invoiceId = $invoice['id'];
        $fees = $otherFees[PaymentFees::TYPE[$type]];

        switch ($type) {
            case FeeType::REGISTRATION:
                $this->storeRegister($registeredDomains, $invoiceId, $fees);
                break;
            case FeeType::RENEW:
                $this->storeRenewal($registeredDomains, $invoiceId, $fees, $otherFees['penalty_total']);
                break;
            case FeeType::TRANSFER:
                $this->storeTransfer($registeredDomains, $invoiceId, $fees);
                break;
            case FeeType::REDEMPTION:
                $this->storeRedemption($registeredDomains, $invoiceId, $fees);
                break;
        }
    }

    public function updatePaymentNodeStatus(int|array $id, string $status, string $email): void
    {
        PaymentNode::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->update(['status' => $status]);

        $log_id = is_int($id) ? $id : implode(',', $id);

        $info = 'Updated payment node with id '.$log_id.' to status '.$status;
        app(AuthLogger::class)->info($this->fromWho($info, $email));
    }

    public function updateByRegisteredDomainId(int|array $id, string $column, mixed $value, string $email): void
    {
        PaymentNode::when(is_int($id), function ($query) use ($id) {
            return $query->where('registered_domain_id', $id)->orderByDesc('id');
        })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('registered_domain_id', $id);
            })
            ->when(($column == 'status' && $value == PaymentNodeStatus::COMPLETED), function ($query) {
                return $query->where('status', PaymentNodeStatus::PENDING);
            })
            ->when(($column == 'status' && $value == PaymentNodeStatus::FAILED), function ($query) {
                return $query->where('status', PaymentNodeStatus::PENDING);
            })
            ->update([$column => $value]);

        $log_id = is_int($id) ? $id : implode(',', $id);

        $info = 'Update payment nodes with registered domain id '.$log_id.' column '.$column.' with value '.$value;
        event(new SystemLogEvent(
            SystemTransactionType::DOMAIN_REGISTRATION,
            "Payment nodes updated by {$email}: registered domain {$log_id}, set {$column} to ".strtolower($value).'',
            null
        ));
        app(AuthLogger::class)->info($this->fromWho($info, $email));
    }

    // PRIVATE FUNCTIONS

    private function storeRegister(Collection $registeredDomains, int $invoiceId, array $registerFee): void
    {
        $castedDomainContent = $this->castToPayload($registeredDomains, $registerFee);
        $this->bulkInsertPaymentNodes($castedDomainContent, $invoiceId);
    }

    private function storeRenewal(Collection $registeredDomains, int $invoiceId, array $renewalFee, string $redemptionFee): void
    {
        $castedDomainContent = $this->castToRenewalPayload($registeredDomains, $renewalFee, $redemptionFee);
        $this->bulkInsertPaymentNodes($castedDomainContent, $invoiceId);
    }

    private function storeTransfer(Collection $registeredDomains, int $invoiceId, array $transferFee): void
    {
        $castedDomainContent = $this->castToPayload(collect($registeredDomains), $transferFee);
        $this->bulkInsertPaymentNodes($castedDomainContent, $invoiceId);
    }

    private function storeRedemption(Collection $registeredDomains, int $invoiceId, float $redemptionFee): void
    {
        $castedDomainContent = $this->castToRedemptionPayload($registeredDomains, $redemptionFee);
        $this->bulkInsertPaymentNodes($castedDomainContent, $invoiceId);
    }

    private function getFeesbyExtension(int $extension_id, array $fee, array $extensions): array
    {
        $ext_name = strtolower($extensions[$extension_id]->name) ?? '';

        return [
            'id' => $fee[$ext_name]['id'] ?? 0,
            'rate' => $fee[$ext_name]['price'] ?? 0,
        ];
    }

    private function castToPayload(Collection $domainContent, array $fee): Collection
    {
        $extensions = DomainTld::getAllExtensionsById();

        return $domainContent->map(function ($e) use ($fee, $extensions) {
            $extension_fee = $this->getFeesbyExtension($e->extension_id, $fee, $extensions);
            $rate = $extension_fee['rate'];
            $fee_id = $extension_fee['id'];
            $total_icann_fee = $e->year_length * $this->icann_fee;
            $total_rate = $e->year_length * $rate;
            $total_amount = $total_rate + $total_icann_fee;

            return [
                'registered_domain_id' => $e->id,
                'extension_fee_id' => $fee_id,
                'year_length' => $e->year_length,
                'rate' => $rate,
                'total_icann_fee' => $total_icann_fee,
                'total_domain_amount' => $total_rate,
                'total_amount' => $total_amount,
                'status' => PaymentNodeStatus::PENDING,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        });
    }

    private function castToRenewalPayload(Collection $domainContent, array $fee, string $redemptionFee): Collection
    {
        $extensions = DomainTld::getAllExtensionsById();

        return $domainContent->map(function ($e) use ($fee, $redemptionFee, $extensions) {
            $extension_fee = $this->getFeesbyExtension($e->extension_id, $fee, $extensions);
            $rate = $extension_fee['rate'];
            $fee_id = $extension_fee['id'];
            $total_icann_fee = $e->year_length * $this->icann_fee;
            $total_rate = $e->year_length * $rate;
            $total_amount = $total_rate + $total_icann_fee;

            return [
                'registered_domain_id' => $e->registered_domain_id,
                'extension_fee_id' => $fee_id,
                'year_length' => $e->year_length,
                'rate' => $rate,
                'total_icann_fee' => $total_icann_fee,
                'redemption_fee' => $redemptionFee,
                'total_domain_amount' => $total_rate,
                'total_amount' => $total_amount,
                'status' => PaymentNodeStatus::PENDING,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        });
    }

    private function castToRedemptionPayload(Collection $domainContent, float $redemptionFee): Collection
    {
        $extensions = DomainTld::getAllExtensionsById();

        return $domainContent->map(function ($e) use ($redemptionFee, $extensions) {
            // For redemption, we use the redemption fee passed as parameter
            $redemptionFeeAmount = $redemptionFee; // This comes from other_fees['redemption_fee']
            $total_icann_fee = $e->year_length * $this->icann_fee;
            $total_amount = $redemptionFeeAmount + $total_icann_fee;

            return [
                'registered_domain_id' => $e->registered_domain_id,
                'extension_fee_id' => 1, // Default fee ID for redemption
                'year_length' => $e->year_length,
                'rate' => $redemptionFeeAmount, // Use redemption fee as the rate
                'total_icann_fee' => $total_icann_fee,
                'redemption_fee' => $redemptionFeeAmount,
                'total_domain_amount' => $redemptionFeeAmount,
                'total_amount' => $total_amount,
                'status' => PaymentNodeStatus::PENDING,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        });
    }

    private function bulkInsertPaymentNodes(Collection $castedDomainContent, int $invoiceId): void
    {
        $paymentNodeTable = (new PaymentNode)->getTable();
        $createdNodes = BulkActions::instance()->bulkInsertGetData($paymentNodeTable, $castedDomainContent->toArray(), false);
        $createdNodeIds = $createdNodes->pluck('id')->toArray();
        $nodeInvoices = [];
        foreach ($createdNodeIds as $id) {
            $nodeInvoice = [
                'payment_invoice_id' => $invoiceId,
                'payment_node_id' => $id,
            ];
            $nodeInvoices[] = $nodeInvoice;
        }
        PaymentNodeInvoice::insert($nodeInvoices);
        app(AuthLogger::class)->info($this->fromWho('Created payment nodes for  '.count($createdNodeIds).' domains.'));
        app(AuthLogger::class)->info($this->fromWho('Created payment node invoices for  '.count($nodeInvoices).' nodes.'));
    }
}
