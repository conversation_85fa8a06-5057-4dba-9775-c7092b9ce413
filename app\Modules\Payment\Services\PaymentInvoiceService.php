<?php

namespace App\Modules\Payment\Services;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Models\PaymentInvoice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\Payment\Constants\PaymentFees;
use App\Modules\Payment\Constants\PaymentInvoiceStatus;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Traits\CursorPaginate;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PaymentInvoiceService
{
    use CursorPaginate, UserLoggerTrait;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $paymentInvoice = new self;

        return $paymentInvoice;
    }

    public function store(array $invoiceData, string $paymentServiceType, int $paymentServiceId): PaymentInvoice
    {
        $userId = $invoiceData['user_id'] ?? $this->getUserId();
        $createdInvoice = $this->storeInvoice($invoiceData, $paymentServiceId);
        app(AuthLogger::class)->info($this->fromWho('Created payment invoice with value '.json_encode($createdInvoice)));
        $this->updateStripePaymentIntent($invoiceData, $createdInvoice, $paymentServiceType);
        $this->createClientActivityEvent($createdInvoice, $userId, $invoiceData['type'], $paymentServiceType);

        return $createdInvoice;
    }

    public function createMultiCheckoutInvoicePayload(array $otherFees, ?string $paymentServiceType, ?string $paymentIntent, int $userId): array
    {
        // FOR payment_summaries
        return [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::MULTI_CHECKOUT_INVOICE],
            'type' => PaymentSummaryType::MULTI_CHECKOUT_INVOICE,
            'paid_amount' => $otherFees['bill_total'] ?? 0,
            'total_amount' => $otherFees['bill_total'] ?? 0,
            'gross_amount' => $otherFees['bill_total'] ?? 0,
            'bill_amount' => $otherFees['bill_total'] ?? 0,
            'payment_intent' => $paymentIntent ?? null,
            'source' => $paymentServiceType,
            'user_id' => $userId,
            'total_payment_node' => $otherFees['domain_count'] ?? 1,
            'net_amount' => 0,
            'service_fee' => 0,
        ];
    }

    public function createInvoicePayload(string $type, int $userId, array $otherFees, ?string $paymentIntent): array
    {
        return [
            'type' => $type,
            'user_id' => $userId ?? $this->getUserId(),
            'other_fees' => $otherFees,
            'payment_intent' => $paymentIntent,
            'total_amount' => $otherFees[PaymentFees::TOTAL[$type]] ?? 0,
            'total_payment_node' => $otherFees['domain_count'] ?? 1,
            'paid_amount' => $otherFees['bill_total'] ?? 0,
            'gross_amount' => $otherFees['bill_total'] ?? 0,
            'bill_amount' => $otherFees['bill_total'] ?? 0,
            'net_amount' => 0,
            'service_fee' => 0,
        ];
    }

    public function createNodeInvoicePayload(string $type, Collection $registeredDomains, array $otherFees)
    {
        return [
            'type' => $type,
            'registered_domains' => $registeredDomains,
            'other_fees' => $otherFees,
        ];
    }

    public function createPaymentPayload(array $invoice, array $nodeInvoice): array
    {
        return [
            'invoice' => $invoice,
            'node_invoice' => $nodeInvoice,
        ];
    }

    public function getPaymentIntentDescription(string $paymentIntent): string
    {
        if (! empty($paymentIntent)) {
            $stripeObj = PaymentIntentProvider::instance()->retrieveIntent($paymentIntent);

            return $stripeObj ? $stripeObj->description : '0';
        }

        return '0';
    }

    public function getInvoiceDataByRegisteredDomain(?int $invoiceId, int $registeredDomainId, int $userId)
    {
        return DB::table('payment_invoices')
            ->join('payment_node_invoices', 'payment_node_invoices.payment_invoice_id', '=', 'payment_invoices.id')
            ->join('payment_nodes', 'payment_nodes.id', '=', 'payment_node_invoices.payment_node_id')
            ->join('payment_services', 'payment_services.id', '=', 'payment_invoices.payment_service_id')
            ->join('extension_fees', 'extension_fees.id', '=', 'payment_nodes.extension_fee_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'payment_nodes.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('tlds', 'tlds.extension_id', '=', 'extension_fees.extension_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('payment_nodes.registered_domain_id', $registeredDomainId)
            ->where('payment_nodes.status', '!=', PaymentNodeStatus::REFUNDED)
            ->when(is_int($invoiceId), function ($query) use ($invoiceId) {
                return $query->where('payment_invoices.id', $invoiceId);
            })
            ->where('users.id', $userId)
            ->orderByDesc('id')
            ->select(
                'payment_invoices.*',
                'payment_node_invoices.id as payment_node_invoice_id',
                'payment_nodes.id as payment_node_id',
                'payment_nodes.registered_domain_id',
                'payment_nodes.year_length',
                'payment_nodes.rate',
                'payment_nodes.total_domain_amount as node_total_amount', // for refund
                'payment_nodes.total_domain_amount', // for registry balance
                'payment_nodes.total_amount as gross_amount',
                'payment_nodes.status as node_status',
                'fees.type as node_type',
                'tlds.registry_id',
                'domains.name as domain_name',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
            )
            ->get()->first();
    }

    public function getInvoiceData(int $id, int $userId): array
    {
        $data = DB::table('payment_invoices')
            ->join('payment_node_invoices', 'payment_node_invoices.payment_invoice_id', '=', 'payment_invoices.id')
            ->leftJoin('payment_reimbursements', 'payment_reimbursements.payment_node_invoice_id', '=', 'payment_node_invoices.id')
            ->join('payment_nodes', 'payment_nodes.id', '=', 'payment_node_invoices.payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'payment_nodes.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('extension_fees', 'extension_fees.id', '=', 'payment_nodes.extension_fee_id')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('payment_services', 'payment_services.id', '=', 'payment_invoices.payment_service_id')
            ->where('payment_invoices.id', $id)
            ->where('payment_services.user_id', $userId)
            ->select(
                'payment_invoices.total_amount as invoice_total_amount',
                'payment_invoices.id as payment_invoices_id',
                'payment_invoices.paid_amount as invoice_paid_amount',
                'payment_invoices.paid_amount as paid_amount',
                'payment_invoices.status as invoice_status',
                'payment_invoices.total_payment_node',
                'payment_invoices.created_at',
                'payment_nodes.id as payment_node_id',
                'payment_nodes.registered_domain_id',
                'payment_nodes.year_length',
                'payment_nodes.rate',
                'payment_nodes.redemption_fee',
                'payment_nodes.total_amount',
                'payment_nodes.total_domain_amount as node_total_amount', // for refund
                'payment_nodes.total_domain_amount', // for registry balance
                'payment_nodes.total_icann_fee',
                'payment_nodes.status as node_status',
                'fees.type as node_type',
                'payment_node_invoices.id as node_invoice_id',
                'payment_reimbursements.id as reimbursement_id',
                'payment_reimbursements.total_amount as reimbursement_total_amount',
                'payment_reimbursements.status as reimbursement_status',
                'payment_services.user_id as user_id',
                'payment_services.id as payment_service_id',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )
            ->get()->all();

        $year_sum = 0;
        $icann_total = 0;
        foreach ($data as $domain) {
            $year_sum += $domain->year_length;
            $icann_total += $domain->total_icann_fee ?? 0;
            $domain->node_type = FeeType::getText($domain->node_type);
        }

        // $icann_total = round($data[0]->invoice_paid_amount - $data[0]->invoice_total_amount - $data[0]->redemption_fee, 2);

        $obj['data'] = $data;
        $obj['icann_total'] = $icann_total;
        $obj['summary_type'] = PaymentSummaryType::PAYMENT_INVOICE;
        $obj['invoice_total_amount'] = $data[0]->invoice_total_amount ?? 0;
        $obj['invoice_paid_amount'] = $data[0]->invoice_paid_amount ?? 0;
        $obj['total_fees'] = $obj['invoice_paid_amount'] - $obj['invoice_total_amount'];
        $obj['subtotal'] = $data[0]->invoice_total_amount ?? 0;
        $obj['paymentIntent'] = $this->getStripePaymentIntent($data[0]->stripe_id ?? 0);

        return $obj;
    }

    // PRIVATE FUNCTIONS
    private function storeInvoice(array $data, int $paymentServiceId): PaymentInvoice
    {
        $invoiceData =
            [
                'total_amount' => $data['total_amount'] ?? 0,
                'paid_amount' => $data['paid_amount'] ?? 0,
                'status' => PaymentInvoiceStatus::PAID,
                'total_payment_node' => $data['total_payment_node'],
                'payment_service_id' => $paymentServiceId,
            ];

        app(AuthLogger::class)->info($this->fromWho('Created payment invoice with value '.implode(',', $invoiceData)));

        return PaymentInvoice::create($invoiceData);
    }

    private function updateStripePaymentIntent(array $invoiceData, PaymentInvoice $createdInvoice, string $paymentServiceType)
    {
        if ($paymentServiceType !== PaymentServiceType::STRIPE) {
            return;
        }

        $intentData = [
            'intent' => $invoiceData['payment_intent'] ?? null,
            'metadata' => [
                'type' => $invoiceData['type'],
                'user_id' => $invoiceData['user_id'] ?? $this->getUserId(),
                'invoice_id' => $createdInvoice['id'],
                'domain_count' => $invoiceData['other_fees']['domain_count'],
            ],
        ];

        PaymentIntentProvider::instance()->update($intentData);
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    // private function createDomainInvoiceSummary(object $invoice, int $userId, string $type, string $paymentServiceType)
    // {
    //     $data = [
    //         'name' => PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_INVOICE].' - '.FeeType::getName($type) ?? '',
    //         'paid_amount' => $invoice->paid_amount ?? 0,
    //         'total_amount' => $invoice->total_amount ?? 0,
    //         'payment_invoice_id' => $invoice->id,
    //         'source' => $paymentServiceType,
    //     ];

    //     event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::PAYMENT_INVOICE));
    // }

    private function createClientActivityEvent(object $invoice, int $userId, string $type, string $paymentServiceType)
    {
        $message = 'Paid $'.$invoice->paid_amount.' for '.PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_INVOICE].' - '.$type ?? '';

        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_INVOICE].' - '.FeeType::getName($type) ?? '',
            'paid_amount' => $invoice->paid_amount ?? 0,
            'total_amount' => $invoice->total_amount ?? 0,
            'payment_invoice_id' => $invoice->id,
            'source' => $paymentServiceType,
        ];

        event(new ClientActivityEvent($userId, UserTransactionType::PAYMENT_SUMMARY, $message, '', $data));
    }

    private function getStripePaymentIntent(int $stripeId)
    {
        if (! $stripeId) {
            return null;
        }

        $stripeTransaction = DB::table('stripe_transactions')
            ->where('id', $stripeId)
            ->get()->first();

        return $stripeTransaction->payment_intent ?? null;
    }
}
