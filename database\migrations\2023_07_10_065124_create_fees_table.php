<?php

use App\Modules\Setting\Constants\FeeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fees', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->float('value');
            $table->timestamps();
        });

        $this->seed();
    }

    public function seed()
    {
        DB::table('fees')->insert([
            ['id' => 1, 'type' => FeeType::REGISTRATION, 'value' => 8],
            ['id' => 2, 'type' => FeeType::RENEW, 'value' => 8],
            ['id' => 3, 'type' => FeeType::TRANSFER, 'value' => 8],
            ['id' => 4, 'type' => FeeType::PROTECTION, 'value' => 1],
            ['id' => 5, 'type' => FeeType::PENALTY_LATE_RENEWAL, 'value' => 150],
            ['id' => 6, 'type' => FeeType::REDEMPTION, 'value' => 100]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fees');
    }
};
