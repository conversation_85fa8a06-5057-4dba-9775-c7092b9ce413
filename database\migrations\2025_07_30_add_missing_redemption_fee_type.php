<?php

use App\Modules\Setting\Constants\FeeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if REDEMPTION fee type already exists
        $redemptionFeeExists = DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->exists();

        if (!$redemptionFeeExists) {
            // Add REDEMPTION fee type
            DB::table('fees')->insert([
                'id' => 6,
                'type' => FeeType::REDEMPTION,
                'value' => 100, // Default redemption fee value
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Add extension_fees entries for REDEMPTION fee type for all extensions
            $extensions = DB::table('extensions')->get();
            $redemptionFeeId = 6;

            foreach ($extensions as $extension) {
                DB::table('extension_fees')->insert([
                    'extension_id' => $extension->id,
                    'fee_id' => $redemptionFeeId,
                    'value' => 100, // Default redemption fee value
                    'is_default' => true,
                    'user_id' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            echo 'Added REDEMPTION fee type and extension fees.' . PHP_EOL;
        } else {
            echo 'REDEMPTION fee type already exists.' . PHP_EOL;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove extension_fees entries for REDEMPTION fee type
        $redemptionFeeId = DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->value('id');

        if ($redemptionFeeId) {
            DB::table('extension_fees')
                ->where('fee_id', $redemptionFeeId)
                ->delete();

            // Remove REDEMPTION fee type
            DB::table('fees')
                ->where('type', FeeType::REDEMPTION)
                ->delete();
        }
    }
};
