<?php

namespace App\Modules\Payment\Services;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Exceptions\UserDomainException;
use App\Models\PaymentReimbursement;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Constants\PaymentReimbursementStatus;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PaymentReimbursementService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $paymentReimbursement = new self;

        return $paymentReimbursement;
    }

    public function store(array $data, int $userId, string $paymentServiceType)
    {
        $invoice = $data['invoice_data'];
        $createdPaymentService = PaymentServiceHelper::instance()->refund($data, $userId, $paymentServiceType);
        $reimbursement = [
            'payment_node_invoice_id' => $invoice->payment_node_invoice_id,
            'total_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'status' => PaymentReimbursementStatus::REQUESTED,
            'payment_service_id' => $createdPaymentService['id'],
        ];

        $createdReimbursement = PaymentReimbursement::create($reimbursement);
        $this->createDomainReimbursementSummary($invoice, $createdReimbursement, $userId, $paymentServiceType);
        $this->createClientActivityEvent($invoice, $userId, $paymentServiceType, $createdReimbursement);

        return $createdReimbursement;
    }

    public function createRefundDetails(string $paymentSummaryType, string $paymentServiceType, ?int $invoiceId)
    {
        return [
            'payment_summary_type' => $paymentSummaryType,
            'payment_service_type' => $paymentServiceType,
            'invoice_id' => $invoiceId ?? null,
        ];
    }

    public function getSummaryRefundById(int $id, int $userId): array
    {
        if (! $id) {
            return [];
        }

        $reimbursement = $this->getById($id, $userId);

        $data = [
            'reimbursement' => $reimbursement,
            'status' => PaymentNodeStatus::REFUNDED,
            'summary_type' => PaymentSummaryType::PAYMENT_REIMBURSEMENT,
        ];

        return $data;
    }

    public function getById(int $id, int $userId): object
    {
        $reimbursement = DB::table('payment_reimbursements as pr')
            ->join('payment_node_invoices as pni', 'pni.id', '=', 'pr.payment_node_invoice_id')
            ->join('payment_nodes as pn', 'pn.id', '=', 'pni.payment_node_id')
            ->join('payment_invoices as pi', 'pi.id', '=', 'pni.payment_invoice_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'pn.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('extension_fees', 'extension_fees.id', '=', 'pn.extension_fee_id')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('payment_services as ps', 'ps.id', '=', 'pr.payment_service_id')
            ->where('pr.id', $id)
            ->where('ps.user_id', $userId)
            ->select(
                'pr.id',
                'pr.payment_node_invoice_id',
                'pr.total_amount',
                'pr.total_amount as current_balance',
                'pr.status as reimbursement_status',
                'pr.payment_service_id as payment_service_id',
                'pr.created_at',
                'ps.user_id as user_id',
                'pi.id as payment_invoice_id',
                'fees.type as node_type',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )->get()->first();

        if (! $reimbursement) {
            // app(AuthLogger::class)->info('get by id: id '.$id);
            // app(AuthLogger::class)->info('get by id: userid '.$userId);

            // app(AuthLogger::class)->info('get by id: userid '.json_encode($reimbursement));
            throw new UserDomainException(403, 'This action is not authorized.', 'Unauthorized');
        }

        return $reimbursement;
    }

    public function getIdByRegisteredDomain(int $registerDomainId): int
    {
        $paymentData = $this->getPaymentData($registerDomainId)->first();

        $reimbursement = DB::table('payment_reimbursements as pr')
            ->where('pr.payment_node_invoice_id', $paymentData->payment_node_invoice_id)
            ->select('*')->get()->first();

        if (! $reimbursement) {
            return 0;
        }

        return $reimbursement->id;
    }

    public function refundByRegisteredDomain(
        int $registeredDomainId,
        int $userId,
        string $transactionType,
        string $description,
        string $refundDestination,
        bool $isCustomRefund = false
    ) {
        $refundDetails = PaymentReimbursementService::instance()->createRefundDetails(
            PaymentSummaryType::PAYMENT_REIMBURSEMENT,
            $refundDestination,
            null,
        );
        // sample transaction type RegistryTransactionType::DOMAIN_TRANSFER
        $refundDetails['description'] = $transactionType.' - '.$description;
        PaymentSummaryService::instance()->createRefund(
            $refundDetails,
            $registeredDomainId,
            $userId,
            $isCustomRefund
        );
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getPaymentData(int|array $registerDomainIds): Collection
    {
        return DB::table('payment_nodes')
            ->join('payment_node_invoices', 'payment_node_invoices.payment_node_id', '=', 'payment_nodes.id')
            ->join('payment_invoices', 'payment_invoices.id', '=', 'payment_node_invoices.payment_invoice_id')
            ->when(is_int($registerDomainIds), function ($query) use ($registerDomainIds) {
                return $query->where('payment_nodes.registered_domain_id', $registerDomainIds);
            })
            ->when(is_array($registerDomainIds), function ($query) use ($registerDomainIds) {
                return $query->whereIn('payment_nodes.registered_domain_id', $registerDomainIds);
            })
            ->orderByDesc('payment_node_invoices.id')
            ->select(
                'payment_nodes.registered_domain_id',
                'payment_nodes.total_domain_amount as node_total_amount', // for refund
                'payment_nodes.total_domain_amount', // for registry balance
                'payment_node_invoices.id as payment_node_invoice_id'
            )
            ->get();
    }

    private function createDomainReimbursementSummary(object $invoice, object $reimbursement, int $userId, string $paymentServiceType)
    {
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_REIMBURSEMENT].' - '.FeeType::getName($invoice->node_type).' - '.$invoice->domain_name,
            'paid_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'total_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'payment_invoice_id' => $reimbursement->id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::PAYMENT_REIMBURSEMENT));
    }

    private function createClientActivityEvent(object $invoice, int $userId, string $paymentServiceType, object $reimbursement)
    {
        $message = 'Refunded $'.$invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0 .' for '.PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_REIMBURSEMENT].' - '.FeeType::TRANSACTION_TYPE[$invoice->node_type].' - '.$invoice->domain_name;
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_REIMBURSEMENT].' - '.FeeType::getName($invoice->node_type).' - '.$invoice->domain_name,
            'paid_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'total_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'payment_invoice_id' => $reimbursement->id,
            'source' => $paymentServiceType,
        ];

        event(new ClientActivityEvent($userId, UserTransactionType::PAYMENT_SUMMARY, $message, '', $data));
    }
}
