<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('support_agent_id')->nullable();
            $table->text('support_agent_name')->nullable();
            $table->text('support_note')->nullable();
            $table->timestamp('feedback_date')->nullable();
        });
    }

    public function down()
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            
        });
    }
};
