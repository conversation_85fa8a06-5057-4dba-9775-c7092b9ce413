<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Util\Constant\QueueErrorTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Config;
use stdClass;

class JobRenewEppService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;

    private JobRecord $jobRecord;

    private const COL_STATUS = 'status';

    public static function instance(): self
    {
        $jobRegisterEppService = new self;

        return $jobRegisterEppService;
    }

    public function handle(JobRecord $record): void
    {
        $this->jobRecord = $record;
        app(AuthLogger::class)->info($this->fromWho('Domain renewal for '.$this->jobRecord->email.' start...', $this->jobRecord->email));

        if ($this->hasExpired($this->jobRecord->domain)) {
            $response = $this->handleExpiredDomain($this->jobRecord->domain);
        } else {
            $response = EppDomainService::instance()->renewDomain($this->jobRecord->domain, $this->jobRecord->email);
        }

        $this->evaluateResponse($response);
    }

    // PRIVATE FUNCTIONS

    private function evaluateResponse(array $response)
    {
        $status = array_key_exists(self::COL_STATUS, $response) ? $response[self::COL_STATUS] : Config::get('domain.status.error');

        if ($status === Config::get('domain.status.ok')) {
            return $this->evaluateRenewSuccess($response);
        }

        // app(AuthLogger::class)->info(json_encode($response));
        $this->evaluateRenewFailed($response);
    }

    private function evaluateRenewSuccess(array $response)
    {
        PaymentNodeService::instance()->updateByRegisteredDomainId($this->jobRecord->registeredDomainId, self::COL_STATUS, PaymentNodeStatus::COMPLETED, $this->jobRecord->email);
        DomainNotificationService::instance()->sendDomainRenewalNotif($this->jobRecord->name, $this->jobRecord->userId);
        $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);
        app(AuthLogger::class)->info($this->fromWho('Domain renewal for '.$this->jobRecord->email.' end...', $this->jobRecord->email));

        JobDispatchService::instance()->refreshEppDispatch($this->jobRecord->getRecord());
        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_RENEWAL',
            'user_id' => $this->jobRecord->userId,
            'status' => 'success',
            'message' => 'Domain "'.$this->jobRecord->name.'" renewed for '.$this->jobRecord->domain->year_length.' year(s)',
            'payload' => $this->jobRecord,
        ]));

        return true;
    }

    private function handleExpiredDomain(stdClass $domain): array
    {
        if ($domain->year_length === 1) {
            return EppDomainService::instance()->databaseSyncExpiry($domain, $this->jobRecord->email);
        }

        $domain->year_length -= 1;

        return EppDomainService::instance()->renewDomain($domain, $this->jobRecord->email);
    }

    private function hasExpired(stdClass $domain): bool
    {
        $currentDate = Carbon::now();
        $expiryDate = Carbon::createFromTimestampMs($domain->expiry);

        return $currentDate->greaterThan($expiryDate) && $domain->status === DomainStatus::EXPIRED;
    }

    private function evaluateRenewFailed(array $response)
    {
        $errorCode = array_key_exists('eppCode', $response) ? $response['eppCode'] : Config::get('domain.code.error');

        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_RENEWAL',
            'user_id' => $this->jobRecord->userId,
            'status' => 'failed',
            'message' => 'Failed to renew domain "'.$this->jobRecord->name.'" - Error code: '.$errorCode,
            'payload' => $this->jobRecord,
        ]));

        switch ($errorCode) {
            case Config::get('domain.code.billing_failure'):
                $this->evaluateBillingFailure();
                break;
            case Config::get('domain.code.parameter_policy_error'):
                $this->evaluateParameterPolicyError();
                break;
            case Config::get('domain.code.object_does_not_exist'):
            case Config::get('domain.code.not_found'):
                $this->evaluateResponseNotFound();
                break;
            default:
                throw new Exception(QueueErrorTypes::RETRY);
        }
    }

    private function evaluateBillingFailure()
    {
        RegistryAccountBalanceService::setBalanceToZero($this->jobRecord->registryId);
        app(AuthLogger::class)->error($this->fromWho('Billing failure', $this->jobRecord->email));

        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function evaluateParameterPolicyError()
    {
        $error = 'Parameter policy error: '.$this->jobRecord->name;
        $this->processFailedTransaction($error, DomainStatus::ACTIVE);
    }

    private function evaluateResponseNotFound()
    {
        $error = 'Object does not exist: '.$this->jobRecord->name;
        $this->processFailedTransaction($error, DomainStatus::NOT_AVAILABLE);
    }

    private function processFailedTransaction(string $error, string $status)
    {
        DomainNotificationService::instance()->sendDomainRenewalFailedNotif($this->jobRecord->name, $this->jobRecord->userId);

        $this->jobRecord->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::RENEW];

        try {
            PaymentSummaryService::instance()->createRefund($this->jobRecord->refundDetails, $this->jobRecord->registeredDomainId, $this->jobRecord->userId);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('error in execute refund: '.$e->getMessage());
        }

        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, $status, true, $this->jobRecord->email);
        // app(AuthLogger::class)->info('job renew update domain status');
        $this->jobRecord->stopJobRetry($status);
        // app(AuthLogger::class)->info('stop job renew retry');
        app(AuthLogger::class)->error($this->fromWho($error, $this->jobRecord->email));
        app(AuthLogger::class)->info('renew process failed transaction end');
    }
}
