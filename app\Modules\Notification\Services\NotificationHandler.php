<?php

namespace App\Modules\Notification\Services;

use App\Events\NotificationEvent;
use App\Util\Constant\TableEvents;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class NotificationHandler
{
    private $notificationPayload = [];

    private $pageUpdatePayload = [];

    private $userId;

    private $timestamp;

    private function __construct(?string $aUserId = null)
    {
        $this->userId = $aUserId;
        $this->timestamp = Carbon::now();
    }

    public static function For(string $userId): NotificationHandler
    {
        return new NotificationHandler($userId);
    }

    public static function Create(): NotificationHandler
    {
        return new NotificationHandler;
    }

    public function store(): NotificationHandler
    {
        if (count($this->notificationPayload) == 0) {
            return $this;
        }

        DB::table('notifications')->insert($this->notificationPayload);
        $this->notificationPayload = [];

        return $this;
    }

    public function addPayload(string $title, string $message, string $route, string $importance, ?array $routeParams = null, ?string $userId = null): NotificationHandler
    {
        $this->notificationPayload[] = [
            'user_id' => $userId ?? $this->userId,
            'title' => $title,
            'message' => $message,
            'redirect_url' => $routeParams ? route($route, $routeParams) : route($route),
            'importance' => $importance,
            'created_at' => $this->timestamp,
            'updated_at' => $this->timestamp,
        ];

        return $this;
    }

    public function notifyOthers(string|array $userIds): NotificationHandler
    {
        $userIds = is_array($userIds) ? $userIds : [$userIds];

        foreach ($userIds as $userId) {
            NotificationEvent::dispatch($userId);
        }

        return $this;
    }

    public function notifySelf(): NotificationHandler
    {
        NotificationEvent::dispatch($this->userId);

        return $this;
    }

    public function addPageUpdatePayload(string|array $pages, ?string $userId = null): NotificationHandler
    {
        $pages = is_array($pages) ? $pages : [$pages];
        $userId = $userId ?? $this->userId;
        $this->pageUpdatePayload[$userId] = $pages;

        return $this;
    }

    public function dispatchPageUpdates(): NotificationHandler
    {
        foreach ($this->pageUpdatePayload as $userId => $user) {
            foreach ($user as $page) {
                $event = TableEvents::EVENTS[$page];
                $event::dispatch($userId);
            }
        }

        $this->pageUpdatePayload = [];

        return $this;
    }

    public function updatePages(string|array $pages): NotificationHandler
    {
        $pages = is_array($pages) ? $pages : [$pages];

        foreach ($pages as $page) {
            $event = TableEvents::EVENTS[$page];
            $event::dispatch($this->userId);
        }

        return $this;
    }
}
