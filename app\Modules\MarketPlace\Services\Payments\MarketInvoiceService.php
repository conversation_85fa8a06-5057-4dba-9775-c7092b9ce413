<?php

namespace App\Modules\MarketPlace\Services\Payments;

use App\Events\ClientActivityEvent;
use App\Events\DomainHistoryEvent;
use App\Models\MarketPlacePaymentInvoice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Constants\MarketPaymentStatus;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Traits\UserContact;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MarketInvoiceService
{
    use UserContact, UserLoggerTrait;

    public static function instance()
    {
        $marketInvoiceService = new self;

        return $marketInvoiceService;
    }

    public function store(array $invoiceData, array|Collection $domain, int $userId, string $paymentServiceType, int $paymentServiceId)
    {
        $data = [
            'payment_service_id' => $paymentServiceId,
            'total_amount' => $invoiceData['total_amount'],
            'paid_amount' => $invoiceData['paid_amount'],
            'status' => $invoiceData['status'],
            'total_payment_node' => $invoiceData['total_payment_node'],
        ];

        $createdInvoice = MarketPlacePaymentInvoice::create($data);
        app(AuthLogger::class)->info($this->fromWho('Created market_payment_invoice with value '.implode(',', $data)));

        return $createdInvoice;
    }

    public function update(int $invoiceId, array $updateDetails)
    {
        DB::table('market_place_payment_invoices')
            ->where('id', $invoiceId)
            ->update($updateDetails);

        app(AuthLogger::class)->info('AfternicReimbursementService: updated invoice to refunded');
    }

    public function getById(int $invoiceId, int $userId)
    {
        return DB::table('market_place_payment_invoices')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_invoice_id', '=', 'market_place_payment_invoices.id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'market_place_payment_invoices.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            // ->where('market_place_payment_invoices.status', '!=', MarketPaymentStatus::REFUNDED)
            ->where('market_place_domains.status', '!=', MarketConstants::STATUS_CANCELLED)
            ->where('market_place_payment_invoices.id', $invoiceId)
            ->where('market_place_domains.user_id', $userId)
            ->select(
                'market_place_payment_invoices.*',
                'market_place_node_invoices.id as market_place_node_invoice_id',
                'market_place_node_invoices.marketplace_payment_node_id',
                'market_place_node_invoices.marketplace_payment_invoice_id',
                'market_place_domains.registered_domain_id',
                'market_place_domains.order_id',
                'market_place_domains.total_amount',
                DB::raw('market_place_domains.total_amount - market_place_domains.total_icann_fee as node_total_amount'), // for refund
                'market_place_domains.total_amount as gross_amount', // from old code
                'market_place_domains.total_amount as rate', // from old code
                'market_place_domains.status as node_status',
                'market_place_domains.total_domain_amount', // for registry balance
                'market_place_domains.vendor',
                'market_place_domains.total_icann_fee',
                'market_place_domains.price',
                'domains.id as domain_id',
                'domains.name as domain_name',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
            )
            ->get()->first();
    }

    public function getInvoiceData(int $invoiceId, int $userId)
    {

        $data = DB::table('market_place_payment_invoices')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_invoice_id', '=', 'market_place_payment_invoices.id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'market_place_payment_invoices.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('market_place_payment_invoices.id', $invoiceId)
            ->where('market_place_domains.user_id', $userId)
            ->select(
                'market_place_payment_invoices.total_amount as invoice_total_amount',
                'market_place_payment_invoices.paid_amount as invoice_paid_amount',
                'market_place_payment_invoices.status as invoice_status',
                'market_place_payment_invoices.total_payment_node',
                'market_place_payment_invoices.created_at',
                'market_place_node_invoices.marketplace_payment_node_id',
                'market_place_node_invoices.marketplace_payment_invoice_id',
                'market_place_domains.registered_domain_id',
                'market_place_domains.order_id',
                'market_place_domains.total_amount',
                DB::raw('market_place_domains.total_amount - market_place_domains.total_icann_fee as node_total_amount'), // for refund
                'market_place_domains.total_amount as gross_amount', // from old code
                'market_place_domains.total_amount as rate', // from old code
                'market_place_domains.status as node_status',
                'market_place_domains.total_domain_amount', // for registry balance
                'market_place_domains.vendor',
                'market_place_domains.total_icann_fee',
                'market_place_domains.price',
                'domains.id as domain_id',
                'domains.name as name',
                'domains.year_length',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
            )
            ->get()->all();

        // $data[0]->node_type = PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_INVOICE];
        $data[0]->summary_type = PaymentSummaryType::MARKETPLACE_INVOICE;

        return $data;
    }

    public function createInvoicePayload(array $request)
    {
        $otherFees = $request['other_fees'];
        $transferPrice = floatval($otherFees['transfer_total'] + $otherFees['icann_fee']);

        return [
            'user_id' => $request['user_id'] ?? $this->getUserId(),
            'total_amount' => $transferPrice ?? 0, // for epp
            'paid_amount' => $otherFees['bill_total'] ?? 0, // from afternic + epp
            'gross_amount' => $otherFees['bill_total'] ?? 0,
            'bill_amount' => $otherFees['bill_total'] ?? 0,
            'net_amount' => 0,
            'service_fee' => 0,
            'status' => MarketPaymentStatus::PAID,
            'total_payment_node' => $otherFees['domain_count'] ?? 1,
            'payment_intent' => $request['intent'] ?? null,
        ];
    }

    public function createNodeInvoicePayload(int $userId, int $domainId, string $orderId, array $domain)
    {

        // event(new DomainHistoryEvent([
        //     'domain_id' => $domainId,
        //     'type' => 'MARKET_TRANSFER_PENDING',
        //     'user_id' => $userId,
        //     'status' => 'success',
        // ]));

        return [
            'user_id' => $userId,
            'registered_domain_id' => $domainId,
            'status' => MarketConstants::DOMAIN_PENDING,
            'order_id' => $orderId,
            'total_amount' => $domain['price'] ?? 0,
        ];
    }

    public function createPaymentPayload(array $request, Collection $registeredDomains, string $orderId): array
    {
        $domain = $request['domains'][0];
        $userId = $request['user_id'] ?? $this->getUserId();
        $invoice = $this->createInvoicePayload($request);

        $nodeInvoice = $this->createNodeInvoicePayload($userId, ($registeredDomains[0])->id, $orderId, $domain);

        return [
            'invoice' => $invoice, // create MarketPlacePaymentInvoice
            'node_invoice' => $nodeInvoice, // StoreMarketPlaceDomainNode MarketPlaceDomains
            'domain' => $domain,
        ];
    }

    // PRIVATE FUNCTION

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function createClientActivityEvent(object $invoice, int $userId, string $domain, string $paymentServiceType)
    {
        $message = 'Paid $'.$invoice->paid_amount.' for '.PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_INVOICE].' - '.$domain;
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_INVOICE].' - '.$domain ?? '',
            'paid_amount' => $invoice->paid_amount ?? 0,
            'total_amount' => $invoice->total_amount ?? 0,
            'payment_market_place_invoice_id' => $invoice->id,
            'source' => $paymentServiceType,
        ];

        event(new ClientActivityEvent($userId, UserTransactionType::PAYMENT_SUMMARY, $message, '', $data));
        app(AuthLogger::class)->info('AfternicCheckout: created paymentsummary');
    }
}
