<?php

namespace App\Util\Constant;

final class Transaction
{
    public final const SYSTEM_LIMIT = 50;

    public final const USER_LIMIT = 5;

    public final const LENGTH = 30; // 30 days

    public final const REGISTER = 'register';

    public final const TRANSFER = 'transfer';

    public final const RENEW = 'renew';

    public final const DELETE = 'delete';

    public final const REDEEM = 'redeem';

    public final const AUTH_REQUEST = 'auth_request';

    public final const DOMAIN_SEARCH = 'domain_search';

    public final const PUSH = 'push';

    public final const TYPES = [
        self::REGISTER,
        self::TRANSFER,
        self::RENEW,
        self::DELETE,
        self::REDEEM,
        self::AUTH_REQUEST,
        self::DOMAIN_SEARCH,
        self::PUSH
    ];
}
