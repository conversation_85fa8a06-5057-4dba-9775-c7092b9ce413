<?php

namespace App\Modules\PaymentSummary\Services\Interfaces;

use App\Events\EmailSent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Exceptions\FailedRequestException;
use App\Mail\Constants\MailConstant;
use App\Mail\RefundSuccess;
use App\Models\PaymentInvoice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Jobs\EmailPaymentInvoice;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Contracts\PaymentSummaryInterface;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\Metadata;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;

class DomainPaymentSummary implements PaymentSummaryInterface
{
    use UserLoggerTrait;

    public function pay(array $data, int $userId, string $paymentServiceType, $createdPaymentService)
    {
        $invoiceData = $data['invoice'];
        $nodeInvoiceData = $data['node_invoice'];

        if (empty($invoiceData) || empty($nodeInvoiceData || ! $userId)) {
            throw new FailedRequestException(400, 'Invalid data for payment invoice.', 'Error');
        }

        $createdInvoice = $this->createInvoice($invoiceData, $paymentServiceType, $createdPaymentService);
        $this->createNodeInvoice($nodeInvoiceData, $createdInvoice);

        return $createdInvoice;
    }

    public function refund(array $data, int $userId, string $refundDestination, bool $isCustomRefund)
    {
        $invoice = PaymentInvoiceService::instance()->getInvoiceDataByRegisteredDomain($data['invoice_id'], $data['registered_domain_id'], $userId);

        if (! $invoice) {
            throw new FailedRequestException(404, 'Invoice not found or already refunded', 'Error');
        }

        $data['invoice_data'] = $invoice;
        app(abstract: AuthLogger::class)->info($this->fromWho(json_encode($invoice), 'CRON'));
        $paymentServiceType = $this->getRefundPaymentServiceType($invoice, $refundDestination, $isCustomRefund);
        $createdReimbursement = PaymentReimbursementService::instance()->store($data, $userId, $paymentServiceType);
        PaymentNodeService::instance()->updatePaymentNodeStatus($invoice->payment_node_id, PaymentNodeStatus::REFUNDED, $invoice->email);
        $this->handleRegistryBalanceRefund($invoice, $data['description']);
        $mailPayload = $this->getMailPayload($invoice, $createdReimbursement, $userId);
        $this->sendRefundMail($invoice, $mailPayload, $userId);
        DomainNotificationService::instance()->sendDomainRefund($invoice->domain_name, $userId);
    }

    public function getPaymentbyId(int $id, int $userId)
    {
        return PaymentInvoiceService::instance()->getInvoiceData($id, $userId);
    }

    public function getRefundbyId(int $id, int $userId)
    {
        return PaymentReimbursementService::instance()->getSummaryRefundById($id, $userId);
    }

    public function createPaymentSummary(array $payload, object $invoice, int $userId, string $paymentServiceType)
    {
        $invoiceData = $payload['invoice'];
        $type = $invoiceData['type'];

        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::PAYMENT_INVOICE].' - '.FeeType::getName($type) ?? '',
            'paid_amount' => $invoice->paid_amount ?? 0,
            'total_amount' => $invoice->bill_amount ?? $invoice->total_amount ?? 0,
            'payment_invoice_id' => $invoice->id,
            'payment_service_id' => $invoice->payment_service_id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::PAYMENT_INVOICE));
    }

    public function getPaymentSummaryByDataId(int $invoiceId, int $userId)
    {
        return PaymentSummaryService::instance()->getByPaymentInvoiceId($invoiceId, $userId);
    }

    public function createPaymentService(array $data, int $userId, string $paymentServiceType)
    {
        $invoiceData = $data['invoice'];
        $nodeInvoiceData = $data['node_invoice'];

        if (empty($invoiceData) || empty($nodeInvoiceData || ! $userId)) {
            throw new FailedRequestException(400, 'Invalid data for payment invoice.', 'Error');
        }

        $createdPaymentService = PaymentServiceHelper::instance()->pay($invoiceData, $userId, $paymentServiceType);

        return $createdPaymentService;
    }

    public function sendInvoice(array $payload, object $invoice, int $userId, string $paymentServiceType)
    {
        // Skip sending email for redemption payments - they are handled in JobRedemptionSuccessService
        if ($invoice->type === FeeType::REDEMPTION) {
            return;
        }

        EmailPaymentInvoice::dispatch($payload['summary_id'], $userId)
            ->onConnection(QueueConnection::PAYMENT_JOB)
            ->onQueue(QueueTypes::PAYMENT_EMAIL);
    }

    // PRIVATE FUNCTIONS

    private function createNodeInvoice(array $nodeInvoice, PaymentInvoice $createdInvoice): void
    {
        $nodeInvoice['invoice'] = $createdInvoice;

        PaymentNodeService::instance()->store($nodeInvoice);
    }

    private function handleRegistryBalanceRefund(object $invoice, string $description)
    {
        $balance = RegistryAccountBalanceService::balance($invoice->registry_id);
        RegistryAccountBalanceService::debit($balance, $invoice->total_domain_amount ?? $invoice->node_total_amount, RegistryTransactionType::ADD_FUND, $description);
    }

    private function getMailPayload(object $invoice, object $reimbursement, int $userId)
    {
        $invoiceNumber = Metadata::getPaymentDescription([
            'user_id' => $userId,
            'invoice_id' => $reimbursement->id,
            'domain_count' => $invoice->total_payment_node,
        ]);

        $paymentSummary = PaymentSummaryService::instance()->getByPaymentInvoiceId($reimbursement->id, $userId);

        return [
            'email' => $invoice->email,
            'name' => $invoice->first_name.' '.$invoice->last_name,
            'refund_type' => FeeType::getText($invoice->node_type),
            'domain' => $invoice->domain_name,
            'refunded_amount' => $invoice->node_total_amount ?? $invoice->total_domain_amount ?? 0,
            'other_data' => [
                'invoice_num' => $invoiceNumber,
                'node_type' => FeeType::getText($invoice->node_type),
                'date' => Carbon::now()->format('Y-m-d H:i'),
                'reimbursementId' => $reimbursement->id,
                'summaryId' => $paymentSummary->id,
            ],
        ];
    }

    private function sendRefundMail(object $invoice, array $mailPayload, $userId)
    {
        try {
            $queueMessage = (new RefundSuccess(['payload' => $mailPayload]))->onConnection(QueueConnection::MAIL_JOB)->onQueue(MailConstant::REFUND_SUCCESS);
            Mail::to($invoice->email)->send($queueMessage);
            $payloadString = json_encode($mailPayload);

            $subject = 'Domain '.ucwords(strtolower($mailPayload['other_data']['node_type'])).' Refund';

            event(new EmailSent(
                $userId,
                $mailPayload['name'],
                $invoice->email,
                $subject,
                $subject,
                $payloadString,
                null
            ));
        } catch (Exception $e) {
            app(abstract: AuthLogger::class)->error($this->fromWho($e->getMessage(), $invoice->email));
        }
    }

    private function createInvoice($invoiceData, $paymentServiceType, $createdPaymentService)
    {
        if (is_array($createdPaymentService)) {
            return $this->createStripeInvoice($invoiceData, $paymentServiceType, $createdPaymentService);
        }

        return PaymentInvoiceService::instance()->store($invoiceData, $paymentServiceType, $createdPaymentService->id);
    }

    private function createStripeInvoice($invoiceData, $paymentServiceType, $createdPaymentService)
    {
        $paymentServiceObj = is_array($createdPaymentService) ? $createdPaymentService['payment_service'] : $createdPaymentService;
        $paymentServiceData = is_array($createdPaymentService) ? $createdPaymentService['data'] : $invoiceData;

        $invoiceData['total_amount'] = $paymentServiceData['bill_amount'] ?? $invoiceData['total_amount'] ?? 0;
        $invoiceData['paid_amount'] = $paymentServiceData['gross_amount'] ?? $invoiceData['paid_amount'] ?? 0;

        return PaymentInvoiceService::instance()->store($invoiceData, $paymentServiceType, $paymentServiceObj->id);
    }

    private function getRefundPaymentServiceType(object $invoice, string $refundDestination, bool $isCustomRefund)
    {
        if ($isCustomRefund) {
            return $refundDestination;
        } else {
            return PaymentServiceHelper::instance()->getPaymentServiceType($invoice);
        }
    }
}
