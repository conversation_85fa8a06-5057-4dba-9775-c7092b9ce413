<?php

namespace App\Modules\DomainRedemption\Services\JobServices;

use App\Events\EmailSent;
use App\Mail\Constants\MailConstant;
use App\Mail\RefundSuccess;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\DomainRedemption\Services\RedemptionPaymentService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Util\Constant\QueueConnection;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class JobRedemptionRecord
{
    use UserLoggerTrait;

    public int $domainId;
    public string $domainName;
    public int $userId;
    public string $email;
    public array $refundDetails;
    public int $registeredDomainId;
    public bool $isSuccessFlow = false;

    private $isJob = true;

    public function __construct(array $params)
    {
        $this->domainId = $params['domainId'];
        $this->domainName = $params['domainName'];
        $this->userId = $params['userId'];
        $this->email = $this->getUserEmail($params['userId']);
        $this->refundDetails = $params['refundDetails'] ?? [];
        $this->registeredDomainId = $this->getRegisteredDomainId($params['domainId']);
    }

    public function processRefund(string $reason = 'EPP Operation Failed'): void
    {
        if ($this->isSuccessFlow) {
            app(AuthLogger::class)->info($this->fromWho("Refund skipped - domain is in success flow: {$this->domainName}", $this->email));
            return;
        }

        try {
            if (isset($this->refundDetails['redemption_order_id'])) {
                $refundSuccess = RedemptionPaymentService::instance()->processRedemptionRefund(
                    $this->refundDetails['redemption_order_id'],
                    FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION] . ' - ' . $reason
                );

                if ($refundSuccess) {
                    $this->resetRedemptionOrder();
                    $this->sendRefundEmail($reason);
                    $this->sendFailureNotification();
                    app(AuthLogger::class)->info($this->fromWho("Redemption refund processed for domain: {$this->domainName}", $this->email));
                } else {
                    app(AuthLogger::class)->error($this->fromWho("Failed to process redemption refund for domain: {$this->domainName}", $this->email));
                }
            } else {
                $this->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION] . ' - ' . $reason;

                PaymentSummaryService::instance()->createRefund(
                    $this->refundDetails,
                    $this->registeredDomainId,
                    $this->userId
                );

                $this->resetRedemptionOrder();
                $this->sendRefundEmail($reason);
                $this->sendFailureNotification();
                app(AuthLogger::class)->info($this->fromWho("Legacy refund processed for domain: {$this->domainName}", $this->email));
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Failed to process refund: {$e->getMessage()}", $this->email));
        }
    }

    private function getUserEmail(int $userId): string
    {
        $user = DB::table('users')->where('id', $userId)->first();
        return $user->email;
    }

    private function getRegisteredDomainId(int $domainId): int
    {
        $registeredDomain = DB::table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        return $registeredDomain ? $registeredDomain->id : 0;
    }

    private function resetRedemptionOrder(): void
    {
        DB::table('redemption_orders')
            ->where('domain_id', $this->domainId)
            ->where('user_id', $this->userId)
            ->update([
                'paid_at' => null,
                'updated_at' => now()
            ]);
    }

    private function sendFailureNotification(): void
    {
        DomainNotificationService::instance()->sendDomainRedemptionFailedNotif($this->domainName, $this->userId);
    }

    private function sendRefundEmail(string $reason): void
    {
        try {
            $user = DB::table('users')->where('id', $this->userId)->first();
            $registeredDomain = DB::table('registered_domains')->where('id', $this->registeredDomainId)->first();

            if (!$user || !$registeredDomain) {
                app(AuthLogger::class)->error($this->fromWho("User or registered domain not found for refund email", $this->email));
                return;
            }

            $mailPayload = [
                'refund_type' => FeeType::REDEMPTION,
                'domain' => $this->domainName,
                'refunded_amount' => $registeredDomain->redemption_fee ?? 0,
                'other_data' => [
                    'invoice_num' => 'RDM-' . $this->domainId,
                    'node_type' => FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION],
                    'date' => Carbon::now()->format('Y-m-d H:i'),
                    'summaryId' => $this->registeredDomainId,
                    'reason' => $reason,
                ],
                'name' => trim($user->first_name . ' ' . $user->last_name),
            ];

            $queueMessage = (new RefundSuccess(['payload' => $mailPayload]))
                ->onConnection(QueueConnection::MAIL_JOB)
                ->onQueue(MailConstant::REFUND_SUCCESS);

            Mail::to($this->email)->send($queueMessage);

            $payloadString = json_encode($mailPayload);
            $subject = 'Domain Redemption Refund';

            event(new EmailSent(
                $this->userId,
                $mailPayload['name'],
                $this->email,
                $subject,
                $subject,
                $payloadString,
                null
            ));

            app(AuthLogger::class)->info($this->fromWho("Refund email sent for domain redemption: {$this->domainName}", $this->email));
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Failed to send refund email: {$e->getMessage()}", $this->email));
        }
    }
}
