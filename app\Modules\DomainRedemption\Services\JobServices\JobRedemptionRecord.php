<?php

namespace App\Modules\DomainRedemption\Services\JobServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\DomainRedemption\Services\RedemptionPaymentService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use Exception;
use Illuminate\Support\Facades\DB;

class JobRedemptionRecord
{
    use UserLoggerTrait;

    public int $domainId;
    public string $domainName;
    public int $userId;
    public string $email;
    public array $refundDetails;
    public int $registeredDomainId;
    public bool $isSuccessFlow = false;

    private $isJob = true;

    public function __construct(array $params)
    {
        $this->domainId = $params['domainId'];
        $this->domainName = $params['domainName'];
        $this->userId = $params['userId'];
        $this->email = $this->getUserEmail($params['userId']);
        $this->refundDetails = $params['refundDetails'] ?? [];
        $this->registeredDomainId = $this->getRegisteredDomainId($params['domainId']);
    }

    public function processRefund(string $reason = 'EPP Operation Failed'): void
    {
        if ($this->isSuccessFlow) {
            app(AuthLogger::class)->info($this->fromWho("Refund skipped - domain is in success flow: {$this->domainName}", $this->email));
            return;
        }

        try {
            if (isset($this->refundDetails['redemption_order_id'])) {
                $refundSuccess = RedemptionPaymentService::instance()->processRedemptionRefund(
                    $this->refundDetails['redemption_order_id'],
                    FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION] . ' - ' . $reason
                );

                if ($refundSuccess) {
                    $this->resetRedemptionOrder();
                    $this->sendFailureNotification();
                    app(AuthLogger::class)->info($this->fromWho("Redemption refund processed for domain: {$this->domainName}", $this->email));
                } else {
                    app(AuthLogger::class)->error($this->fromWho("Failed to process redemption refund for domain: {$this->domainName}", $this->email));
                }
            } else {
                $this->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION] . ' - ' . $reason;

                PaymentSummaryService::instance()->createRefund(
                    $this->refundDetails,
                    $this->registeredDomainId,
                    $this->userId
                );

                $this->resetRedemptionOrder();
                $this->sendFailureNotification();
                app(AuthLogger::class)->info($this->fromWho("Legacy refund processed for domain: {$this->domainName}", $this->email));
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Failed to process refund: {$e->getMessage()}", $this->email));
        }
    }

    private function getUserEmail(int $userId): string
    {
        $user = DB::table('users')->where('id', $userId)->first();
        return $user->email;
    }

    private function getRegisteredDomainId(int $domainId): int
    {
        $registeredDomain = DB::table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        return $registeredDomain ? $registeredDomain->id : 0;
    }

    private function resetRedemptionOrder(): void
    {
        DB::table('redemption_orders')
            ->where('domain_id', $this->domainId)
            ->where('user_id', $this->userId)
            ->update([
                'paid_at' => null,
                'updated_at' => now()
            ]);
    }

    private function sendFailureNotification(): void
    {
        DomainNotificationService::instance()->sendDomainRedemptionFailedNotif($this->domainName, $this->userId);
    }
}
