<?php

namespace Database\Seeders;

use App\Util\Constant\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TransactionThresholdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->insertTransactions();
        $this->insertThresholdSubscribers();
        $this->insertTransactionTriggers();
        $this->insertTriggerSubscribers();
    }


    private function insertTransactions()
    {
        $payload = [];
        $systemLimit = Transaction::SYSTEM_LIMIT;
        $userLimit = Transaction::USER_LIMIT;
        $length = Transaction::LENGTH;
        $now = Carbon::now();

        foreach (Transaction::TYPES as $transaction) {
            $payload[] = [
                'name' => $transaction,
                'system_limit' => $systemLimit,
                'user_limit' => $userLimit,
                'length' => $length,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        DB::table('transactions')->insert($payload);
    }

    private function insertThresholdSubscribers()
    {
        DB::table('threshold_subscribers')->insert(['admin_id' => 1]);
    }


    private function insertTransactionTriggers()
    {
        $transactionIds = DB::table('transactions')->pluck('id')->toArray();

        $payload = [];
        $now = Carbon::now();

        foreach ($transactionIds as $id) {
            $payload[] = [
                'transaction_id' => $id,
                'notify_subscriber' => true,
                'allow_action' => false,
                'updated_at' => $now
            ];
        }

        DB::table('transaction_triggers')->insert($payload);
    }

    private function insertTriggerSubscribers()
    {
        $payload = [];
        $systemLimit = Transaction::SYSTEM_LIMIT;
        $userLimit = Transaction::USER_LIMIT;
        $length = Transaction::LENGTH;
        $now = Carbon::now();

        foreach (Transaction::TYPES as $transaction) {
            $payload[] = [
                'name' => $transaction,
                'system_limit' => $systemLimit,
                'user_limit' => $userLimit,
                'length' => $length,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        DB::table('transactions')->insert($payload);
    }
}
