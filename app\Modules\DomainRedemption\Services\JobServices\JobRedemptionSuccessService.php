<?php

namespace App\Modules\DomainRedemption\Services\JobServices;

use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Mail\Constants\MailConstant;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\DomainService;
use Carbon\Carbon;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionRecord;
use App\Modules\DomainRedemption\Services\RedemptionPaymentService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Jobs\EmailPaymentInvoice;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use Exception;
use Illuminate\Support\Facades\DB;

class JobRedemptionSuccessService
{
    use UserLoggerTrait;

    private JobRedemptionRecord $jobRecord;

    public static function instance(): self
    {
        return new self;
    }

    public function handle(JobRedemptionRecord $record): void
    {
        $this->jobRecord = $record;
        $this->jobRecord->isSuccessFlow = true;

        app(AuthLogger::class)->info($this->fromWho("Domain redemption success processing started: {$this->jobRecord->domainName}", $this->jobRecord->email));

        $this->processSuccessRedemption();

        app(AuthLogger::class)->info($this->fromWho("Domain redemption completed: {$this->jobRecord->domainName}", $this->jobRecord->email));
    }

    private function processSuccessRedemption(): void
    {
        $eppInfoResponse = $this->callEppInfo();
        $this->evaluateResponse($eppInfoResponse, 'EPP Info Verification');

        // app(AuthLogger::class)->info($this->fromWho("EPP Info Response: " . json_encode($eppInfoResponse), $this->jobRecord->email));

        if ($this->isStatusOkay($eppInfoResponse)) {
            $restoreAcceptedResponse = $this->callEppRestoreAccepted();
            $this->evaluateResponse($restoreAcceptedResponse, 'EPP Restore Accepted');

            $this->restoreDomainFromDeletion();
            $this->updateDomainExpiry($eppInfoResponse);
            $this->restoreDomainToActive();
            $this->completePaymentNode();
            $this->markRedemptionPaymentCompleted();
            $this->completeRedemptionOrder();
            $this->sendPaymentSummaryEmail();
            $this->sendNotification();
            $this->updateUserDomainTable();
            $this->logDomainHistory();

        } else {
            $errorMessage = "Domain not found in EPP system, cannot proceed with restore acceptance";
            app(AuthLogger::class)->error($this->fromWho($errorMessage, $this->jobRecord->email));

            throw new Exception($errorMessage);
        }
    }

    private function sendNotification(): void
    {
        DomainNotificationService::instance()->sendDomainRedemptionSuccessNotif($this->jobRecord->domainName, $this->jobRecord->userId);
    }

    private function completeRedemptionOrder(): void
    {
        DB::table('redemption_orders')
            ->where('domain_id', $this->jobRecord->domainId)
            ->where('user_id', $this->jobRecord->userId)
            ->update([
                'deleted_at' => now(),
                'updated_at' => now()
            ]);
    }

    private function callEppInfo(): array
    {
        return EppDomainService::instance()->callEppInfo($this->jobRecord->domainName);
    }

    private function callEppRestoreAccepted(): array
    {
        return EppDomainService::instance()->callEppRestoreAccepted($this->jobRecord->domainName);
    }

    private function isStatusOkay(array $eppInfoResponse): bool
    {
        if (isset($eppInfoResponse['data']) && !empty($eppInfoResponse['data'])) {
            return true;
        }

        app(AuthLogger::class)->info($this->fromWho("Domain not found in EPP system or no data returned", $this->jobRecord->email));
        return false;
    }

    private function evaluateResponse(array $response, string $operation): void
    {
        if (!$this->isEppResponseSuccessful($response)) {
            $errorDetails = $this->extractErrorDetails($response);
            $errorMessage = "{$operation} failed: {$errorDetails}";
            app(AuthLogger::class)->error($this->fromWho($errorMessage, $this->jobRecord->email));

            if ($this->isRetryableError($response)) {
                throw new Exception(QueueErrorTypes::RETRY);
            }

            throw new Exception($errorMessage);
        }
    }

    private function isRetryableError(array $response): bool
    {
        if (isset($response['eppCode'])) {
            $retryableCodes = [
                '2400', 
                '2500', 
                '2502', 
            ];

            return in_array($response['eppCode'], $retryableCodes);
        }

        return true;
    }

    private function extractErrorDetails(array $response): string
    {
        if (isset($response['status']['message'])) {
            $message = $response['status']['message'];
            $eppCode = $response['status']['eppCode'] ?? 'Unknown';
            return "EPP Error {$eppCode}: {$message}";
        }

        if (isset($response['errors'])) {
            return is_array($response['errors']) ? implode(', ', $response['errors']) : $response['errors'];
        }

        return json_encode($response);
    }

    private function isEppResponseSuccessful(array $response): bool
    {
        if (isset($response['status'])) {
            if ($response['status'] === 'OK' || $response['status'] === 'success') {
                return true;
            }

            if (is_array($response['status'])) {
                $statusCode = $response['status']['statusCode'] ?? null;
                $status = $response['status']['status'] ?? null;

                if (in_array($statusCode, [200, 1000, 1001])) {
                    return true;
                }

                if (in_array($status, ['FORBIDDEN', 'ERROR', 'FAILED'])) {
                    return false;
                }
            }
        }

        if (isset($response['statusCode']) && in_array($response['statusCode'], [200, 1000, 1001])) {
            return true;
        }

        if (!isset($response['errors']) && !isset($response['error']) && !isset($response['status']['message'])) {
            return true;
        }

        return false;
    }

    private function restoreDomainFromDeletion(): void
    {
        DB::table('registered_domains')
            ->where('id', $this->jobRecord->registeredDomainId)
            ->update([
                'deleted_at' => null,
                'status' => UserDomainStatus::OWNED,
                'updated_at' => now()
            ]);

        app(AuthLogger::class)->info($this->fromWho("Domain restored from deletion queue: {$this->jobRecord->domainName}", $this->jobRecord->email));
    }

    private function updateDomainExpiry(array $eppInfoResponse): void
    {
        if (isset($eppInfoResponse['data']['expiry'])) {
            $eppExpiry = $eppInfoResponse['data']['expiry'];
            $expiryTimestamp = Carbon::parse($eppExpiry)->valueOf();

            DB::table('domains')
                ->where('id', $this->jobRecord->domainId)
                ->update([
                    'expiry' => $expiryTimestamp,
                    'updated_at' => now()
                ]);

            // app(AuthLogger::class)->info($this->fromWho("Domain expiry updated to {$eppExpiry}: {$this->jobRecord->domainName}", $this->jobRecord->email));
        }
    }

    private function restoreDomainToActive(): void
    {
        DomainService::instance()->updateDomainStatus(
            $this->jobRecord->domainId,
            DomainStatus::ACTIVE,
            false,
            $this->jobRecord->email
        );

        app(AuthLogger::class)->info($this->fromWho("Domain status updated to ACTIVE: {$this->jobRecord->domainName}", $this->jobRecord->email));
    }

    private function completePaymentNode(): void
    {
        PaymentNodeService::instance()->updateByRegisteredDomainId(
            $this->jobRecord->registeredDomainId,
            'status',
            PaymentNodeStatus::COMPLETED,
            $this->jobRecord->email
        );

        app(AuthLogger::class)->info($this->fromWho("Payment node marked as completed: {$this->jobRecord->domainName}", $this->jobRecord->email));
    }

    private function markRedemptionPaymentCompleted(): void
    {
        $redemptionOrderId = DB::table('redemption_orders')
            ->where('domain_id', $this->jobRecord->domainId)
            ->where('user_id', $this->jobRecord->userId)
            ->value('id');

        if ($redemptionOrderId) {
            $redemptionPayment = RedemptionPaymentService::instance()->getRedemptionPaymentService($redemptionOrderId);

            if ($redemptionPayment) {
                app(AuthLogger::class)->info($this->fromWho("Redemption payment service completed: Order {$redemptionOrderId}, Payment Service {$redemptionPayment->payment_service_id}", $this->jobRecord->email));
            } else {
                app(AuthLogger::class)->error($this->fromWho("No redemption payment service found for order: {$redemptionOrderId}", $this->jobRecord->email));
            }
        } else {
            app(AuthLogger::class)->error($this->fromWho("No redemption order found for domain: {$this->jobRecord->domainName}", $this->jobRecord->email));
        }
    }

    private function updateUserDomainTable(): void
    {
        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
        app(AuthLogger::class)->info($this->fromWho("Domain table updated for user: {$this->jobRecord->userId}", $this->jobRecord->email));
    }

    private function logDomainHistory(): void
    {
        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_REDEMPTION_COMPLETED',
            'user_id' => $this->jobRecord->userId,
            'status' => 'success',
            'message' => "Domain '{$this->jobRecord->domainName}' redemption completed successfully by {$this->jobRecord->email}. Domain restored to ACTIVE status.",
            'payload' => $this->jobRecord,
        ]));
    }

    private function sendPaymentSummaryEmail(): void
    {
        try {
            $redemptionOrderId = DB::table('redemption_orders')
                ->where('domain_id', $this->jobRecord->domainId)
                ->where('user_id', $this->jobRecord->userId)
                ->value('id');

            if ($redemptionOrderId) {
                $redemptionPayment = RedemptionPaymentService::instance()->getRedemptionPaymentService($redemptionOrderId);

                if ($redemptionPayment && isset($redemptionPayment->payment_summary_id)) {
                    EmailPaymentInvoice::dispatch($redemptionPayment->payment_summary_id, $this->jobRecord->userId, PaymentSummaryType::PAYMENT_INVOICE)
                        ->onConnection(QueueConnection::MAIL_JOB)
                        ->onQueue(MailConstant::PAYMENT_INVOICE);

                    app(AuthLogger::class)->info($this->fromWho("Payment summary email dispatched for redemption: {$this->jobRecord->domainName}, Summary ID: {$redemptionPayment->payment_summary_id}", $this->jobRecord->email));
                } else {
                    app(AuthLogger::class)->error($this->fromWho("No payment summary found for redemption order: {$redemptionOrderId}", $this->jobRecord->email));
                }
            } else {
                app(AuthLogger::class)->error($this->fromWho("No redemption order found for domain: {$this->jobRecord->domainName}", $this->jobRecord->email));
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Failed to send payment summary email: {$e->getMessage()}", $this->jobRecord->email));
        }
    }
}
