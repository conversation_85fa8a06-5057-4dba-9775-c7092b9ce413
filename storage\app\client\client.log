[2025-07-24 02:39:13] local.INFO: mjb<PERSON><PERSON><PERSON>@gmail.com Created stripe intent: usd 5280  
[2025-07-24 02:39:20] local.INFO: mjb<PERSON>lo<PERSON>@gmail.com Created stripe intent: usd 5280  
[2025-07-24 02:39:27] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3RoF41AYkyIIc8ES1wjlqFhT","object":"payment_intent","amount":5280,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":5280,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3RoF41AYkyIIc8ES1wjlqFhT_secret_tr8IFruKK3dkUehjmv8mI3dlW","confirmation_method":"automatic","created":1753324761,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3RoF41AYkyIIc8ES1fcXhha4","object":"charge","amount":5280,"amount_captured":5280,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3RoF41AYkyIIc8ES1rlH7cmn","object":"balance_transaction","amount":5280,"available_on":1753660800,"balance_type":"payments","created":1753324767,"currency":"usd","description":null,"exchange_rate":null,"fee":183,"fee_details":[{"amount":183,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":5097,"reporting_category":"charge","source":"ch_3RoF41AYkyIIc8ES1fcXhha4","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Miake","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1753324761,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":14,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RoF41AYkyIIc8ES1wjlqFhT","payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_details":{"card":{"amount_authorized":5280,"authorization_code":"998481","brand":"visa","capture_before":1753929561,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":5280,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKOC5hsQGMgafe9QxuJ86LBayo5Ce-6tOct43dMY1JfHqThTrwzGNUmCaJFYoy8T6uto9nWULDw6N1km3","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-07-24 02:39:27] local.INFO: getStripeIntentById balance:: {"id":"txn_3RoF41AYkyIIc8ES1rlH7cmn","object":"balance_transaction","amount":5280,"available_on":1753660800,"balance_type":"payments","created":1753324767,"currency":"usd","description":null,"exchange_rate":null,"fee":183,"fee_details":[{"amount":183,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":5097,"reporting_category":"charge","source":"ch_3RoF41AYkyIIc8ES1fcXhha4","status":"pending","type":"charge"}  
[2025-07-24 02:39:27] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"redemption_fee":"50.00","redemption_total":"50.00","icann_fee":0.18,"bill_total":50.18,"domain_count":1,"year_sum":1},"payment_intent":"pi_3RoF41AYkyIIc8ES1wjlqFhT","total_amount":"50.00","total_payment_node":1,"paid_amount":50.18,"gross_amount":52.8,"bill_amount":50.18,"net_amount":50.97,"service_fee":1.83,"adjustments":0.79,"charge_id":"ch_3RoF41AYkyIIc8ES1fcXhha4"}  
[2025-07-24 02:39:27] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":52.8,"net_amount":50.97,"service_fee":1.83,"charge_id":"ch_3RoF41AYkyIIc8ES1fcXhha4","payment_intent":"pi_3RoF41AYkyIIc8ES1wjlqFhT"}  
[2025-07-24 02:39:27] local.INFO: <EMAIL> Created payment invoice with value 50.18,52.8,PAID,1,12  
[2025-07-24 02:39:27] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":50.18,"paid_amount":52.8,"status":"PAID","total_payment_node":1,"payment_service_id":12,"updated_at":"2025-07-24T02:39:27.000000Z","created_at":"2025-07-24T02:39:27.000000Z","id":10}  
[2025-07-24 02:39:28] local.INFO: <EMAIL> Updated stripe intent for : 7-10-1  
[2025-07-24 02:39:29] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-07-24 02:39:29] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-07-24 02:39:29] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-07-24 02:39:29] local.INFO: <EMAIL> Redemption payment service created: Order 6, Payment Service 12  
[2025-07-24 02:39:29] local.INFO: <EMAIL> Redemption payment service created: {"redemption_order_id":6,"payment_service_id":12,"updated_at":"2025-07-24T02:39:29.000000Z","created_at":"2025-07-24T02:39:29.000000Z","id":5}  
[2025-07-24 02:39:29] local.INFO: <EMAIL> Redemption job dispatched: redmonday.com  
[2025-07-24 02:40:31] local.INFO: <EMAIL> Domain redemption started: redmonday.com  
[2025-07-24 02:40:36] local.INFO: <EMAIL> Redemption success job dispatched: redmonday.com  
[2025-07-24 02:41:36] local.INFO: <EMAIL> Domain redemption success processing started: redmonday.com  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Domain restored from deletion queue: redmonday.com  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Domain expiry updated to 2026-02-20T08:04:28Z: redmonday.com  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Updated domain id 94 to status ACTIVE  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Domain status updated to ACTIVE: redmonday.com  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Update payment nodes with registered domain id 16 column status with value COMPLETED  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Payment node marked as completed: redmonday.com  
[2025-07-24 02:41:40] local.INFO: <EMAIL> Redemption payment service completed: Order 6, Payment Service 12  
[2025-07-24 02:41:41] local.INFO: <EMAIL> Domain table updated for user: 7  
[2025-07-24 02:41:41] local.INFO: Domain History: Domain 'redmonday.com' redemption completed <NAME_EMAIL>. Domain restored to ACTIVE status.  
[2025-07-24 02:41:41] local.INFO: <EMAIL> Domain redemption completed: redmonday.com  
[2025-07-24 02:41:41] local.INFO: <EMAIL> Domain redemption success processing completed: redmonday.com  
[2025-07-24 04:59:19] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-07-28 02:31:01] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-07-28 05:04:00] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-28 05:06:47] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-29 05:03:35] local.ERROR: {"error":"Illuminate\\Validation\\ValidationException","message":"The name should only contain letters and spaces.","url":"http:\/\/www.mydomain.strangedomains.local\/wire-transfer","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(32): Illuminate\\Foundation\\Http\\FormRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\BankTransfer\\Requests\\StoreWireTransferRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\BankTransfer\\Requests\\StoreWireTransferRequest), Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Ban...', Object(App\\Modules\\BankTransfer\\Requests\\StoreWireTransferRequest))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Ban...', Object(App\\Modules\\BankTransfer\\Requests\\StoreWireTransferRequest))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Ban...', Array, true)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Ban...', Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Ban...', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Ban...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\BankTransfer\\Controllers\\BankTransferController), 'store')
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\BankTransfer\\Controllers\\BankTransferController), 'store')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\BankTransfer\\Controllers\\BankTransferController), 'store')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 {main}"}  
[2025-07-30 03:19:11] local.INFO: <EMAIL> Created stripe intent: usd 10510  
[2025-07-30 03:19:19] local.INFO: <EMAIL> Created stripe intent: usd 10510  
[2025-07-30 03:19:26] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3RqQXzAYkyIIc8ES1C7MsieG","object":"payment_intent","amount":10510,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":10510,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3RqQXzAYkyIIc8ES1C7MsieG_secret_rPqtgHhrllwvpTrJl7i6zISG7","confirmation_method":"automatic","created":1753845559,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3RqQXzAYkyIIc8ES1mU2Nr0I","object":"charge","amount":10510,"amount_captured":10510,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3RqQXzAYkyIIc8ES18fEhKtV","object":"balance_transaction","amount":10510,"available_on":1754006400,"balance_type":"payments","created":1753845565,"currency":"usd","description":null,"exchange_rate":null,"fee":335,"fee_details":[{"amount":335,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":10175,"reporting_category":"charge","source":"ch_3RqQXzAYkyIIc8ES1mU2Nr0I","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Miake","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1753845559,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":55,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RqQXzAYkyIIc8ES1C7MsieG","payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_details":{"card":{"amount_authorized":10510,"authorization_code":"863694","brand":"visa","capture_before":1754450359,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":10510,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKL6epsQGMgbR9DHD5uI6LBY-YOELhCYUVA0DmI0UNyadwB4nKg2iLpredZgoN1y7SytPkeGbjOCs30wZ","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-07-30 03:19:26] local.INFO: getStripeIntentById balance:: {"id":"txn_3RqQXzAYkyIIc8ES18fEhKtV","object":"balance_transaction","amount":10510,"available_on":1754006400,"balance_type":"payments","created":1753845565,"currency":"usd","description":null,"exchange_rate":null,"fee":335,"fee_details":[{"amount":335,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":10175,"reporting_category":"charge","source":"ch_3RqQXzAYkyIIc8ES1mU2Nr0I","status":"pending","type":"charge"}  
[2025-07-30 03:19:26] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"redemption_fee":"100.00","redemption_total":"100.00","icann_fee":0.18,"bill_total":100.18,"domain_count":1,"year_sum":1},"payment_intent":"pi_3RqQXzAYkyIIc8ES1C7MsieG","total_amount":"100.00","total_payment_node":1,"paid_amount":100.18,"gross_amount":105.1,"bill_amount":100.18,"net_amount":101.75,"service_fee":3.35,"adjustments":1.57,"charge_id":"ch_3RqQXzAYkyIIc8ES1mU2Nr0I"}  
[2025-07-30 03:19:26] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":105.1,"net_amount":101.75,"service_fee":3.35,"charge_id":"ch_3RqQXzAYkyIIc8ES1mU2Nr0I","payment_intent":"pi_3RqQXzAYkyIIc8ES1C7MsieG"}  
[2025-07-30 03:19:26] local.INFO: <EMAIL> Created payment invoice with value 100.18,105.1,PAID,1,18  
[2025-07-30 03:19:26] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":100.18,"paid_amount":105.1,"status":"PAID","total_payment_node":1,"payment_service_id":18,"updated_at":"2025-07-30T03:19:26.000000Z","created_at":"2025-07-30T03:19:26.000000Z","id":11}  
[2025-07-30 03:19:27] local.INFO: <EMAIL> Updated stripe intent for : 7-11-1  
[2025-07-30 03:19:28] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-07-30 03:19:28] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-07-30 03:19:29] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-07-30 03:19:29] local.INFO: <EMAIL> Redemption payment service created: Order 5, Payment Service 18  
[2025-07-30 03:19:29] local.INFO: <EMAIL> Redemption payment service created: {"redemption_order_id":5,"payment_service_id":18,"updated_at":"2025-07-30T03:19:29.000000Z","created_at":"2025-07-30T03:19:29.000000Z","id":6}  
[2025-07-30 03:19:29] local.INFO: <EMAIL> Redemption job dispatched: biogenesis.net  
[2025-07-30 03:20:31] local.INFO: <EMAIL> Domain redemption started: biogenesis.net  
[2025-07-30 03:20:36] local.INFO: <EMAIL> Redemption success job dispatched: biogenesis.net  
[2025-07-30 03:21:37] local.INFO: <EMAIL> Domain redemption success processing started: biogenesis.net  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Domain restored from deletion queue: biogenesis.net  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Updated domain id 92 to status ACTIVE  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Domain status updated to ACTIVE: biogenesis.net  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Update payment nodes with registered domain id 18 column status with value COMPLETED  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Payment node marked as completed: biogenesis.net  
[2025-07-30 03:21:40] local.INFO: <EMAIL> Redemption payment service completed: Order 5, Payment Service 18  
[2025-07-30 03:21:40] local.ERROR: <EMAIL> No payment summary found for redemption order: 5  
[2025-07-30 03:21:41] local.INFO: <EMAIL> Domain table updated for user: 7  
[2025-07-30 03:21:41] local.INFO: Domain History: Domain 'biogenesis.net' redemption completed <NAME_EMAIL>. Domain restored to ACTIVE status.  
[2025-07-30 03:21:41] local.INFO: <EMAIL> Domain redemption completed: biogenesis.net  
[2025-07-30 03:27:48] local.INFO: <EMAIL> Created stripe intent: usd 10510  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Created payment invoice with value 100.18,100.18,PAID,1,19  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":100.18,"paid_amount":100.18,"status":"PAID","total_payment_node":1,"payment_service_id":19,"updated_at":"2025-07-30T03:27:53.000000Z","created_at":"2025-07-30T03:27:53.000000Z","id":12}  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-07-30 03:27:53] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Redemption payment service created: Order 4, Payment Service 19  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Redemption payment service created: {"redemption_order_id":4,"payment_service_id":19,"updated_at":"2025-07-30T03:27:53.000000Z","created_at":"2025-07-30T03:27:53.000000Z","id":7}  
[2025-07-30 03:27:53] local.INFO: <EMAIL> Redemption job dispatched: redmonday.net  
[2025-07-30 03:28:54] local.INFO: <EMAIL> Domain redemption started: redmonday.net  
[2025-07-30 03:28:59] local.INFO: <EMAIL> Redemption success job dispatched: redmonday.net  
[2025-07-30 03:29:59] local.INFO: <EMAIL> Domain redemption success processing started: redmonday.net  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Domain restored from deletion queue: redmonday.net  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Updated domain id 93 to status ACTIVE  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Domain status updated to ACTIVE: redmonday.net  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Update payment nodes with registered domain id 17 column status with value COMPLETED  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Payment node marked as completed: redmonday.net  
[2025-07-30 03:30:03] local.INFO: <EMAIL> Redemption payment service completed: Order 4, Payment Service 19  
[2025-07-30 03:30:03] local.ERROR: <EMAIL> No payment summary found for redemption order: 4  
[2025-07-30 03:30:04] local.INFO: <EMAIL> Domain table updated for user: 7  
[2025-07-30 03:30:04] local.INFO: Domain History: Domain 'redmonday.net' redemption completed <NAME_EMAIL>. Domain restored to ACTIVE status.  
[2025-07-30 03:30:04] local.INFO: <EMAIL> Domain redemption completed: redmonday.net  
[2025-07-30 03:30:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:30:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:30:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:30:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:30:18] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-30 03:46:39] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-07-30 03:46:55] local.INFO: <EMAIL> Created stripe intent: usd 10510  
[2025-07-30 03:47:16] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3RqQyhAYkyIIc8ES0jOTA5w4","object":"payment_intent","amount":10510,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":10510,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3RqQyhAYkyIIc8ES0jOTA5w4_secret_PRO9dTRpgYkmVdLfLIUf5Plel","confirmation_method":"automatic","created":1753847215,"currency":"usd","customer":null,"description":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3RqQyhAYkyIIc8ES0jXQswrD","object":"charge","amount":10510,"amount_captured":10510,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3RqQyhAYkyIIc8ES0TbMCs04","object":"balance_transaction","amount":10510,"available_on":1754006400,"balance_type":"payments","created":1753847234,"currency":"usd","description":null,"exchange_rate":null,"fee":335,"fee_details":[{"amount":335,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":10175,"reporting_category":"charge","source":"ch_3RqQyhAYkyIIc8ES0jXQswrD","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":null,"phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1753847229,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":24,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RqQyhAYkyIIc8ES0jOTA5w4","payment_method":"pm_1RqQyvAYkyIIc8ESEoYgI8Ul","payment_method_details":{"card":{"amount_authorized":10510,"authorization_code":"822590","brand":"visa","capture_before":1754452029,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":10510,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKMOrpsQGMgafdxp-c-A6LBZjYPwrcNfa1HMm22HHYtDdLzX_b1Y-lHSWiHYgDLxTbTMMJLmyuqx5OzhA","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RqQyvAYkyIIc8ESEoYgI8Ul","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-07-30 03:47:16] local.INFO: getStripeIntentById balance:: {"id":"txn_3RqQyhAYkyIIc8ES0TbMCs04","object":"balance_transaction","amount":10510,"available_on":1754006400,"balance_type":"payments","created":1753847234,"currency":"usd","description":null,"exchange_rate":null,"fee":335,"fee_details":[{"amount":335,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":10175,"reporting_category":"charge","source":"ch_3RqQyhAYkyIIc8ES0jXQswrD","status":"pending","type":"charge"}  
[2025-07-30 03:47:16] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"redemption_fee":"100.00","redemption_total":"100.00","icann_fee":0.18,"bill_total":100.18,"domain_count":1,"year_sum":1},"payment_intent":"pi_3RqQyhAYkyIIc8ES0jOTA5w4","total_amount":"100.00","total_payment_node":1,"paid_amount":100.18,"gross_amount":105.1,"bill_amount":100.18,"net_amount":101.75,"service_fee":3.35,"adjustments":1.57,"charge_id":"ch_3RqQyhAYkyIIc8ES0jXQswrD"}  
[2025-07-30 03:47:16] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":105.1,"net_amount":101.75,"service_fee":3.35,"charge_id":"ch_3RqQyhAYkyIIc8ES0jXQswrD","payment_intent":"pi_3RqQyhAYkyIIc8ES0jOTA5w4"}  
[2025-07-30 03:47:16] local.INFO: <EMAIL> Created payment invoice with value 100.18,105.1,PAID,1,20  
[2025-07-30 03:47:16] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":100.18,"paid_amount":105.1,"status":"PAID","total_payment_node":1,"payment_service_id":20,"updated_at":"2025-07-30T03:47:16.000000Z","created_at":"2025-07-30T03:47:16.000000Z","id":13}  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Updated stripe intent for : 7-13-1  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-07-30 03:47:17] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Redemption payment service created: Order 12, Payment Service 20  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Redemption payment service created: {"redemption_order_id":12,"payment_service_id":20,"updated_at":"2025-07-30T03:47:17.000000Z","created_at":"2025-07-30T03:47:17.000000Z","id":8}  
[2025-07-30 03:47:17] local.INFO: <EMAIL> Redemption job dispatched: cdforfree.net  
[2025-07-30 03:48:18] local.INFO: <EMAIL> Domain redemption started: cdforfree.net  
[2025-07-30 03:48:23] local.INFO: <EMAIL> Redemption success job dispatched: cdforfree.net  
[2025-07-30 03:49:23] local.INFO: <EMAIL> Domain redemption success processing started: cdforfree.net  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Domain restored from deletion queue: cdforfree.net  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Updated domain id 96 to status ACTIVE  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Domain status updated to ACTIVE: cdforfree.net  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Update payment nodes with registered domain id 14 column status with value COMPLETED  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Payment node marked as completed: cdforfree.net  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Redemption payment service completed: Order 12, Payment Service 20  
[2025-07-30 03:49:27] local.INFO: <EMAIL> Payment summary email dispatched for redemption: cdforfree.net, Summary ID: 10  
[2025-07-30 03:49:28] local.INFO: <EMAIL> Domain table updated for user: 7  
[2025-07-30 03:49:28] local.INFO: Domain History: Domain 'cdforfree.net' redemption completed <NAME_EMAIL>. Domain restored to ACTIVE status.  
[2025-07-30 03:49:28] local.INFO: <EMAIL> Domain redemption completed: cdforfree.net  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-07-30 03:49:33] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
