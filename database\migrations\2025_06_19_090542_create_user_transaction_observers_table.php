<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_transaction_observers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('system_transaction_observer_id')->constrained('system_transaction_observers');
            $table->foreignId('user_transaction_id')->constrained('user_transactions');
            $table->foreignId('transaction_trigger_id')->constrained('transaction_triggers');
            $table->integer('past_counter');
            $table->date('sync_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_transaction_observers');
    }
};
