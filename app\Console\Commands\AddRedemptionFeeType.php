<?php

namespace App\Console\Commands;

use App\Modules\Setting\Constants\FeeType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddRedemptionFeeType extends Command
{
    protected $signature = 'fees:add-redemption';
    protected $description = 'Add missing REDEMPTION fee type to fees and extension_fees tables';

    public function handle()
    {
        $redemptionFeeExists = DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->exists();

        if ($redemptionFeeExists) {
            $this->info('REDEMPTION fee type already exists.');
            return;
        }

        DB::table('fees')->insert([
            'id' => 6,
            'type' => FeeType::REDEMPTION,
            'value' => 100,
        ]);

        $extensions = DB::table('extensions')->get();
        $redemptionFeeId = 6;

        foreach ($extensions as $extension) {
            DB::table('extension_fees')->insert([
                'extension_id' => $extension->id,
                'fee_id' => $redemptionFeeId,
                'value' => 100,
                'is_default' => true,
                'user_id' => null,
            ]);
        }

        $this->info('Successfully added REDEMPTION fee type and extension fees.');
    }
}
