<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Jobs\RefreshEppDomain;
use App\Modules\Domain\Jobs\RegisterEppDomain;
use App\Modules\Domain\Jobs\RedemptionEppDomain;
use App\Modules\Domain\Jobs\RenewEppDomain;
use App\Modules\Domain\Jobs\UpdateEppDomain;
use App\Modules\DomainClassificationApi\Jobs\DomainClassificationApiInsertNewDomainJob;
use Exception;
use Illuminate\Support\Facades\Bus;
use Throwable;

class JobDispatchService
{
    use UserLoggerTrait;

    private $isJob = true;

    private $maxAttemptError = 3;

    public static function instance(): self
    {
        $jobDispatchService = new self;

        return $jobDispatchService;
    }

    public function registerEppDispatch(array $registerPayload, array $updatePayload, int $seconds = 0): void
    {
        Bus::chain([
            (new RegisterEppDomain($this->validateRegisterEppDomainPayload($registerPayload)))->delay($seconds),
            new UpdateEppDomain($this->validateUpdateEppDomainPayload($updatePayload)),
            new DomainClassificationApiInsertNewDomainJob($updatePayload),
        ])->catch(function (Throwable $e) {
            app(AuthLogger::class)->error($e->getMessage());
        })->dispatch();
    }

    public function renewEppDispatch(array $domainPayload, int $seconds = 0): void
    {
        RenewEppDomain::dispatch($this->validateRenewEppDomainPayload($domainPayload))->delay($seconds);
    }

    public function updateRestrictionsEppDispatch(array $domainPayload, int $seconds = 0): void
    {
        UpdateEppDomain::dispatch($this->validateUpdateEppDomainPayload($domainPayload))->delay($seconds);
    }

    public function updateDetailsEppDispatch(array $domainPayload, int $seconds = 0): void
    {
        UpdateEppDomain::dispatch($this->validateUpdateEppDomainPayload($domainPayload))->delay($seconds);
    }

    public function updateEppDispatch(array $domainPayload, int $seconds = 0): void // delete
    {
        UpdateEppDomain::dispatch($this->validateUpdateEppDomainPayload($domainPayload))->delay($seconds);
    }

    public function updatePostAutoRenewalGracePeriodDispatch(array $domainPayload, int $seconds = 0): void // delete
    {
        UpdateEppDomain::dispatch($this->validateUpdateEppDomainPayload($domainPayload))->delay($seconds);
    }

    public function refreshEppDispatch(array $domainPayload, int $seconds = 0): void
    {
        RefreshEppDomain::dispatch($this->validateRefreshEppDomainPayload($domainPayload))->delay($seconds);
    }

    // public function redemptionEppDispatch(array $domainPayload, int $seconds = 0): void
    // {
    //     RedemptionEppDomain::dispatch($this->validateRedemptionEppDomainPayload($domainPayload))->delay($seconds);
    // }

    // PRIVATE FUNCTIONS

    private function validateRegisterEppDomainPayload(array $domain)
    {
        return [
            JobPayloadKeys::DOMAIN => (object) $this->checkKeys(JobPayloadKeys::DOMAIN, $domain),
            JobPayloadKeys::REGISTERED_DOMAIN => (object) $this->checkKeys(JobPayloadKeys::REGISTERED_DOMAIN, $domain),
            JobPayloadKeys::REGISTRY => $this->checkKeys(JobPayloadKeys::REGISTRY, $domain),
            JobPayloadKeys::USER_ID => $this->checkKeys(JobPayloadKeys::USER_ID, $domain),
            JobPayloadKeys::EMAIL => $this->checkKeys(JobPayloadKeys::EMAIL, $domain),
            JobPayloadKeys::REFUND_DATA => $this->checkKeys(JobPayloadKeys::REFUND_DATA, $domain),
        ];
    }

    private function validateUpdateEppDomainPayload(array $domain)
    {
        return [
            JobPayloadKeys::DOMAIN => (object) $this->checkKeys(JobPayloadKeys::DOMAIN, $domain),
            JobPayloadKeys::REGISTERED_DOMAIN => (object) $this->checkKeys(JobPayloadKeys::REGISTERED_DOMAIN, $domain),
            JobPayloadKeys::REGISTRY => $this->checkKeys(JobPayloadKeys::REGISTRY, $domain),
            JobPayloadKeys::USER_ID => $this->checkKeys(JobPayloadKeys::USER_ID, $domain),
            JobPayloadKeys::EMAIL => $this->checkKeys(JobPayloadKeys::EMAIL, $domain),
            JobPayloadKeys::UPDATE_TYPE => $this->checkKeys(JobPayloadKeys::UPDATE_TYPE, $domain),
        ];
    }

    private function validateRenewEppDomainPayload(array $domain)
    {
        return [
            JobPayloadKeys::DOMAIN => (object) $this->checkKeys(JobPayloadKeys::DOMAIN, $domain),
            JobPayloadKeys::REGISTERED_DOMAIN => (object) $this->checkKeys(JobPayloadKeys::REGISTERED_DOMAIN, $domain),
            JobPayloadKeys::USER_ID => $this->checkKeys(JobPayloadKeys::USER_ID, $domain),
            JobPayloadKeys::REGISTRY => $this->checkKeys(JobPayloadKeys::REGISTRY, $domain),
            JobPayloadKeys::EMAIL => $this->checkKeys(JobPayloadKeys::EMAIL, $domain),
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_RENEWAL,
            JobPayloadKeys::REFUND_DATA => $this->checkKeys(JobPayloadKeys::REFUND_DATA, $domain),
        ];
    }

    private function validateRefreshEppDomainPayload(array $domain)
    {
        return [
            JobPayloadKeys::DOMAIN => (object) $this->checkKeys(JobPayloadKeys::DOMAIN, $domain),
            JobPayloadKeys::REGISTERED_DOMAIN => (object) $this->checkKeys(JobPayloadKeys::REGISTERED_DOMAIN, $domain),
            JobPayloadKeys::USER_ID => $this->checkKeys(JobPayloadKeys::USER_ID, $domain),
            JobPayloadKeys::REGISTRY => $this->checkKeys(JobPayloadKeys::REGISTRY, $domain),
            JobPayloadKeys::EMAIL => $this->checkKeys(JobPayloadKeys::EMAIL, $domain),
            JobPayloadKeys::UPDATE_TYPE => $this->checkKeys(JobPayloadKeys::UPDATE_TYPE, $domain),
        ];
    }

    // private function validateRedemptionEppDomainPayload(array $domain)
    // {
    //     return [
    //         JobPayloadKeys::DOMAIN => (object) $this->checkKeys(JobPayloadKeys::DOMAIN, $domain),
    //         JobPayloadKeys::REGISTERED_DOMAIN => (object) $this->checkKeys(JobPayloadKeys::REGISTERED_DOMAIN, $domain),
    //         JobPayloadKeys::USER_ID => $this->checkKeys(JobPayloadKeys::USER_ID, $domain),
    //         JobPayloadKeys::REGISTRY => $this->checkKeys(JobPayloadKeys::REGISTRY, $domain),
    //         JobPayloadKeys::EMAIL => $this->checkKeys(JobPayloadKeys::EMAIL, $domain),
    //         JobPayloadKeys::UPDATE_TYPE => $this->checkKeys(JobPayloadKeys::UPDATE_TYPE, $domain),
    //         JobPayloadKeys::REFUND_DATA => array_key_exists(JobPayloadKeys::REFUND_DATA, $domain) ? $domain[JobPayloadKeys::REFUND_DATA] : null,
    //     ];
    // }

    private function checkKeys(string $key, array $data)
    {
        return array_key_exists($key, $data) ? $data[$key] : $this->throwEmptyError($key);
    }

    private function throwEmptyError(string $key)
    {
        throw new Exception('No data for key: '.$key);
    }
}
