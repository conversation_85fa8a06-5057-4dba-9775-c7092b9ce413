<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\MarketPlaceVendors;
use App\Modules\MarketPlace\Services\DomainTransferService;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AfternicDomainTransfer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    private $domainInfo;

    private $registeredDomains;

    /**
     * Create a new job instance.
     */
    public function __construct($domain, $authCode, $yearLength, $email, $orderID, $registered_domain_id, $user_id, $id)
    {
        $this->params = [
            'id' => $id,
            'domain' => $domain,
            'authCode' => $authCode,
            'yearLength' => $yearLength,
            'email' => $email,
            'orderID' => $orderID,
            'registeredDomainId' => $registered_domain_id,
            'userId' => $user_id,
        ];

        $this->onConnection(QueueConnection::MARKET_PLACE_JOBS);
        $this->onQueue(MarketPlaceVendors::AFTERNIC);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            DomainTransferService::instance()->initiateDomainTransfer($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->info('AfternicDomainTask: Error: '.$e->getMessage());
            $this->fail();
        }
    }
}
