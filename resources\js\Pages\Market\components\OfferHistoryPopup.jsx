import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify';

export default function OfferHistoryPopup({modal, showModal, domain, getStatus}) {

    const [history, setHistory] = useState([]);

    useEffect(() => {
        if(modal) {
            axios.post(route('marketoffer.history'), { id: domain.id })
            .then((data) => {
                setHistory(data.data)
            })
        } else {
            setHistory([])
        }
    }, [modal])

    const getPrice = (domain) => {
        if(domain.offer_status == 'counter_offer') return domain.counter_offer_price
        else return domain.offer_price
    }

    return (
        <div className={` ${modal ? '' : 'hidden'} fixed z-10 overflow-y-auto top-0 w-full left-0`} id="modal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                    <div className="inline-block align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                        <div className="bg-white px-4 pt-4 pb-4 sm:p-6 sm:pb-4">
                            <label className="text-lg text-gray-800">Offer History - <span className="text-primary font-bold">{domain.domain_name}</span></label>
                            {
                                history ? <div className='flex flex-col'>
                                    <div className='grid grid-cols-5 pt-8 capitalize'>
                                        <span>Date</span>
                                        <span className='-ml-14'>Status</span>
                                        <span className='-ml-20'>Initial Price</span>
                                        <span className='-ml-36'>Buy Now Price</span>
                                        <span className='-ml-28'>Feedback</span>
                                    </div>
                                    <div className='max-h-[200px] border-y my-2 h-full overflow-y-scroll overflow-x-hidden'>
                                        {
                                            history.map((a) => {
                                                return <div key={a.id} className='grid grid-cols-5 pt-4 capitalize'>
                                                    <span>{(new Date(a.created_at).toLocaleString()).split(',')[0]}</span>
                                                    <span className='-ml-14'>{getStatus(a.offer_status)}</span>
                                                    <span className='-ml-20'>${a.offer_price}</span>
                                                    <span className='-ml-36'>${a.counter_offer_price}</span>
                                                    <span className='-ml-28'>{a.feedback ? a.feedback : 'none'}</span>
                                                </div>
                                            })
                                        }
                                    </div>
                                </div> : <></>
                            }
                        </div>
                        <div className="px-4 py-3 text-right">
                            <button type="button" onClick={() => { showModal(false); }} className="cursor-pointer py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-700 mr-2" ><i className="fas fa-times"></i> Close </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
