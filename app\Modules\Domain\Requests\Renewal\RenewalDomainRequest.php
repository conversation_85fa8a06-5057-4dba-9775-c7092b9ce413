<?php

namespace App\Modules\Domain\Requests\Renewal;

use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\UpdateServices\RenewalDomainService;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Rules\Domain\DomainOwnedArrayExists;
use App\Rules\Payment\ValidateOtherFees;
use App\Rules\Payment\ValidateStripeFees;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RenewalDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::PAYMENT_INVOICE]);
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);
            $this->cancelIntent();
        }

        RenewalDomainService::instance()->checkAndCreditRegistryBalance($this->all());
        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        $this->cancelIntent();
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function update(): string
    {
        return RenewalDomainService::instance()->update($this->all());
    }

    // PRIVATE Functions

    private function getValidationRules(): array
    {
        $rules = [
            'domains' => ['required', 'array', 'min:1', new DomainOwnedArrayExists],
            'other_fees' => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::RENEW)],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) {
            PaymentServiceType::STRIPE => array_merge(
                $rules,
                [
                    'intent' => ['required', 'string'],
                    'stripe_fees' => ['required', 'array', 'min:1', new ValidateStripeFees],
                ]
            ),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge($rules, ['amount_to_use' => ['required', 'numeric']]),
            default => $rules
        };
    }

    private function cancelIntent()
    {
        if ($this->input('intent')) {
            PaymentIntentProvider::instance()->cancelIntent($this->input('intent'));
        }
    }

    private function captureIntent(): void
    {
        if (($this->payment_service_type === PaymentServiceType::STRIPE) &&
            $this->input('intent')
        ) {
            PaymentIntentProvider::instance()->captureIntent($this->input('intent'));
        }
    }
}
